<?php

declare(strict_types=1);

use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;
use Phinx\Migration\AbstractMigration;

class AddingMailboxUnscannableItemsRelatedEmails extends AbstractMigration
{
    public const UNSCANNABLE_EMAILS = [
        'unscannable-post-item-release',
        'unscannable-post-item-extra-quota-usage-release',
        'unscannable-post-item-awaiting-payment-to-forward',
    ];

    public function up(): void
    {
        $helper = new NodeMigrationHelper($this);
        $mailboxEmailsFolderId = $helper->getExistingNodeId('mailbox_emails');
        
        if (!$mailboxEmailsFolderId) {
            throw new \RuntimeException('Mailbox emails folder not found. Please run the mailbox emails folder migration first.');
        }

        $order = 0;

        foreach (self::UNSCANNABLE_EMAILS as $emailName) {
            // Check if email already exists
            if ($helper->getExistingNodeId($emailName)) {
                continue;
            }

            $page = new Page($this->convertToTitleCase($emailName));
            $properties = [
                new Property('from', '<EMAIL>'),
                new Property('fromName', 'Companies Made Simple'),
                new Property('subject', ''),
                new Property('tag1', 'mailbox-email'),
                new Property('tag2', 'unscannable-post-item'),
                new Property('tag3', $emailName),
                new Property('templateName', $emailName),
            ];

            $order = $order + 10;
            $node = new Node(
                $page,
                $properties,
                $emailName,
                $mailboxEmailsFolderId,
                null,
                'EmailAdminControler',
                $order
            );

            $helper->create($node);
        }
    }

    public function down(): void
    {
        $helper = new NodeMigrationHelper($this);

        foreach (self::UNSCANNABLE_EMAILS as $emailName) {
            $nodeId = $helper->getExistingNodeId($emailName);
            
            if (!$nodeId) {
                continue;
            }

            $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
            $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
            $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
        }
    }

    private function convertToTitleCase(string $string): string
    {
        $parts = explode('-', $string);

        $convertedParts = array_map(function ($part) {
            return ucfirst(mb_strtolower($part));
        }, $parts);

        return implode(' ', $convertedParts);
    }
}
