<?php

declare(strict_types=1);

namespace spec\CompaniesHouseModule\Services;

use CompaniesHouseModule\Deciders\CHExtraFeeChargeDecider;
use CompaniesHouseModule\Deciders\CompaniesHouseReadinessDecider;
use CompaniesHouseModule\Emailers\SubmissionQueueEmailer;
use CompaniesHouseModule\Entities\IdSubmissionQueue;
use CompaniesHouseModule\Entities\ReviewSubmissionQueue;
use CompaniesHouseModule\Repositories\IdSubmissionQueueRepository;
use CompaniesHouseModule\Repositories\ReviewSubmissionQueueRepository;
use CompaniesHouseModule\Repositories\SubmissionQueueRepository;
use CompaniesHouseModule\Services\SubmissionHandler;
use CompanyFormationModule\Repositories\FormSubmissionRepository;
use Entities\Company;
use Entities\Customer;
use IdModule\Helpers\IdEntityHelper;
use IdModule\Repositories\EntityProvider;
use IdModule\Repositories\IdInfoRepository;
use IdModule\Repositories\IIdCompanyInfoRepository;
use IdModule\Repositories\IIdCustomerInfoRepository;
use OrmModule\Repositories\IPersistRepository;
use PhpSpec\ObjectBehavior;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

/**
 * @mixin SubmissionHandler
 */
class SubmissionHandlerSpec extends ObjectBehavior
{
    /**
     * @var IIdCustomerInfoRepository
     */
    private $customerInfoRepository;

    /**
     * @var IPersistRepository
     */
    private $repository;

    /**
     * @var IdSubmissionQueueRepository
     */
    private $submissionQueueRepository;

    /**
     * @var SubmissionQueueEmailer
     */
    private $emailer;

    /**
     * @var EventDispatcherInterface
     */
    private $eventDispatcher;

    /**
     * @var IIdCompanyInfoRepository
     */
    private $iIdCompanyInfoRepository;

    /**
     * @var CompaniesHouseReadinessDecider
     */
    private $companiesHouseReadinessDecider;

    /**
     * @var CHExtraFeeChargeDecider
     */
    private $chExtraFeeChargeDecider;

    /**
     * @var EntityProvider
     */
    private $entityProvider;

    /**
     * @var IdInfoRepository
     */
    private $idEntityInfoRepository;

    /**
     * @var FormSubmissionRepository
     */
    private $formSubmissionRepository;

    /**
     * @var ReviewSubmissionQueueRepository
     */
    private $reviewSubmissionQueueRepository;

    public function let(
        IPersistRepository $repository,
        IdSubmissionQueueRepository $submissionQueueRepository,
        SubmissionQueueEmailer $emailer,
        EventDispatcherInterface $eventDispatcher,
        IIdCompanyInfoRepository $iIdCompanyInfoRepository,
        CHExtraFeeChargeDecider $chExtraFeeChargeDecider,
        EntityProvider $entityProvider,
        IdInfoRepository $idEntityInfoRepository,
        FormSubmissionRepository $formSubmissionRepository,
        ReviewSubmissionQueueRepository $reviewSubmissionQueueRepository
    ) {
        $this->repository = $repository;
        $this->submissionQueueRepository = $submissionQueueRepository;
        $this->emailer = $emailer;
        $this->eventDispatcher = $eventDispatcher;
        $this->iIdCompanyInfoRepository = $iIdCompanyInfoRepository;
        $this->chExtraFeeChargeDecider = $chExtraFeeChargeDecider;
        $this->entityProvider = $entityProvider;
        $this->idEntityInfoRepository = $idEntityInfoRepository;
        $this->formSubmissionRepository = $formSubmissionRepository;
        $this->reviewSubmissionQueueRepository = $reviewSubmissionQueueRepository;

        $this->beConstructedWith(
            $repository,
            $submissionQueueRepository,
            $emailer,
            $eventDispatcher,
            $iIdCompanyInfoRepository,
            $chExtraFeeChargeDecider,
            $entityProvider,
            $idEntityInfoRepository,
            $formSubmissionRepository,
            $reviewSubmissionQueueRepository
        );
    }

    public function it_is_initializable()
    {
        $this->shouldHaveType(SubmissionHandler::class);
    }

    public function it_should_be_able_to_submit_company_without_id(): void
    {
        $company = new Company(Customer::temporary('test'), 'test1');
        $entity = IdEntityHelper::createInvalidCompanyInfo($company, ['Rita' => []]);

        $this->chExtraFeeChargeDecider->isIncorporationExtraFeeChargeApplicable($company)->willReturn(false);
        $this->chExtraFeeChargeDecider->isConfirmationStatementExtraFeeChargeApplicable($company)->willReturn(false);

        $this->iIdCompanyInfoRepository->getIdInfoForCompany($company)->willReturn($entity);
        $this->canSubmitCompany($company)->shouldBe(false);
    }

    public function it_should_be_able_to_submit_company_valid(): void
    {
        $company = new Company(Customer::temporary('test'), 'test1');
        $customerInfo = IdEntityHelper::createValidIdEntity($company, 'Jonas');

        $this->chExtraFeeChargeDecider->isIncorporationExtraFeeChargeApplicable($company)->willReturn(false);
        $this->chExtraFeeChargeDecider->isConfirmationStatementExtraFeeChargeApplicable($company)->willReturn(false);

        $this->iIdCompanyInfoRepository->getIdInfoForCompany($company)->willReturn(IdEntityHelper::createCompanyInfo($company, []));
        $this->canSubmitCompany($company)->shouldBe(true);
    }

    public function it_should_not_be_able_to_submit_company_invalid(): void
    {
        $company = new Company(Customer::temporary('test'), 'test1');

        $entity = IdEntityHelper::createInvalidCompanyInfo($company, ['Rita' => []]);

        $this->chExtraFeeChargeDecider->isIncorporationExtraFeeChargeApplicable($company)->willReturn(false);
        $this->chExtraFeeChargeDecider->isConfirmationStatementExtraFeeChargeApplicable($company)->willReturn(false);

        $this->iIdCompanyInfoRepository->getIdInfoForCompany($company)->willReturn($entity);
        $this->canSubmitCompany($company)->shouldBe(false);
    }

    public function it_should_not_be_able_to_submit_company_for_invalid_regulated_body(): void
    {
        $company = new Company(Customer::temporary('test'), 'test1');
        $entity = IdEntityHelper::createInvalidCompanyInfo($company, ['Rita' => []]);

        $this->chExtraFeeChargeDecider->isIncorporationExtraFeeChargeApplicable($company)->willReturn(false);
        $this->chExtraFeeChargeDecider->isConfirmationStatementExtraFeeChargeApplicable($company)->willReturn(false);

        $this->iIdCompanyInfoRepository->getIdInfoForCompany($company)->willReturn($entity);
        $this->canSubmitCompany($company)->shouldBe(false);
    }

    //    function it_should_with_hold_submission()
    //    {
    //        $this->repository->saveEntity($queue)->willReturn($queue);
    //        $this->emailer->sendWithHoldSubmission($company)->shouldBeCalled();
    //        $this->withHoldSubmission($company, 'a3')->shouldBe($queue);
    //    }

    //    function it_should_submit()
    //    {
    //        //@TODO update legacy code to test
    //    }

    public function it_should_remove_submission_from_queue(Company $company, IdSubmissionQueue $queue): void
    {
        $formSubmissionId = 123;
        $this->submissionQueueRepository->optionalOneBy(
            ['company' => $company, 'formSubmissionId' => $formSubmissionId]
        )->willReturn($queue);
        $this->repository->removeEntity($queue)->shouldBeCalled();
        $this->removeSubmissionFromQueue($company, $formSubmissionId);
    }

    //
    //    function it_should_reject()
    //    {
    //        $company = new Company(Customer::temporary('test'), 'test1');
    //        $queue = new SubmissionQueue($company, 'a3');
    //        $this->repository->saveEntity($company)->willReturn($company);
    //        $this->repository->removeEntity($queue)->willReturn($queue);
    //        $this->reject($queue);
    //    }

    public function it_should_remove_company_submissions(): void
    {
        $company = new Company(Customer::temporary('test'), 'test1');
        $queue[] = new IdSubmissionQueue($company, 'a3');
        $queue[] = new IdSubmissionQueue($company, 'a2');
        $this->submissionQueueRepository->findBy(['company' => $company])->willReturn($queue);
        $this->repository->removeEntity($queue[0])->shouldBeCalled()->willReturn($queue);
        $this->repository->removeEntity($queue[1])->shouldBeCalled()->willReturn($queue);
        $this->removeSubmissionsFromQueueForCompany($company);
    }

    public function it_should_remove_review_submission_queue(Company $company, ReviewSubmissionQueue $queue): void
    {
        $formSubmissionId = 123;
        $this->reviewSubmissionQueueRepository->optionalOneBy(
            ['company' => $company, 'formSubmissionId' => $formSubmissionId]
        )->willReturn($queue);
        $this->removeReviewSubmissionFromQueue($company, $formSubmissionId);
    }
}
