<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\Controllers;

use CompanyFormationModule\Deciders\RegistrationReviewDecider;
use CompanyFormationModule\Providers\StepProvider;
use CompanyIncorporationModule\Dto\Enum\Step;
use CompanyIncorporationModule\Dto\Enum\SubStep;
use CompanyIncorporationModule\Services\IncorporationProcessLogService;
use CompanyIncorporationModule\Services\StepService;
use CompanyIncorporationModule\Services\StepUrlService;
use Entities\Company;
use FrontModule\controlers\CompaniesCustomerControler;
use JMS\Serializer\Exception\Exception as JMSSerializerException;
use Psr\Log\LoggerInterface;
use RouterModule\Helpers\IControllerHelper;
use RouterModule\Helpers\IMessageHelper;
use Symfony\Component\HttpFoundation\Response;
use TemplateModule\Renderers\IRenderer;

readonly class CallAndMessagesStepController
{
    public const CALL_AND_MESSAGES_FILLED_EVENT = 'company_incorporation.call_and_messages_filled';

    public function __construct(
        private IRenderer $renderer,
        private LoggerInterface $logger,
        private IControllerHelper $controllerHelper,
        private StepService $stepService,
        private StepUrlService $stepUrlService,
        private IncorporationProcessLogService $incorporationProcessLogService,
        private RegistrationReviewDecider $registrationReviewDecider,
    ) {
    }

    public function index(Company $company): Response
    {
        try {
            $callsAndMessagesUrl = $this->stepUrlService->getCallAndMessagesStepUrl($company->getId());
            $stepData = $this->stepService->getStepData($company, StepProvider::STEP_CALL_AND_MESSAGES);
            $phoneNumberSuggestions = [$company->getCustomer()->getPhone(), $company->getCustomer()->getAdditionalPhone()];

            $renderData = [
                'company' => $company->getSimplifiedCompanyData(),
                'phoneNumberSuggestions' => array_filter($phoneNumberSuggestions),
                'companyId'=> $company->getId(),
                'tabSteps' => $stepData['tabSteps'],
                'backUrl' => $callsAndMessagesUrl,
                'serializedCurrentStep' => $this->stepService->getSerializedCurrentStep(
                    $this->stepService->getTabs($company),
                    StepProvider::STEP_CALL_AND_MESSAGES),
                'seo' => ['title' => 'Call & Messages'],
                'isUnderReview' => $this->registrationReviewDecider->isInReviewQueue($company),
            ];

            $this->incorporationProcessLogService->create(
                $company,
                true,
                Step::COMPANY_INCORPORATION_CALL_AND_MESSAGES,
                SubStep::NO_SUB_STEP,
                $callsAndMessagesUrl,
                $renderData
            );

            return $this->renderer->render($renderData);
        } catch (\Exception|JMSSerializerException $e) {
            $this->logger->error('Unable to access Call & Messages page', ['exception' => $e]);
            $this->controllerHelper->setFlashMessage($e->getMessage(), IMessageHelper::MESSAGE_ERROR);

            return $this->controllerHelper->redirectionTo(CompaniesCustomerControler::COMPANIES_PAGE);
        }
    }
}
