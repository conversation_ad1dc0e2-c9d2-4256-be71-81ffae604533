<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\Controllers\Api;

use CompanyIncorporationModule\Exceptions\BusinessServices\InvalidInformation;
use CompanyIncorporationModule\UseCase\BusinessServices\Api;
use Entities\Company;
use RouterModule\ApiController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class BusinessServicesStepApiController extends ApiController
{
    public function __construct(
        private readonly Api\BuildAdditionalInformation\Command $buildAdditionalInformationCommand,
        private readonly Api\BuildAdditionalInformation\Factory $buildAdditionalInformationFactory,
        private readonly Api\GetBankOffers\Command $getBankOffersCommand,
        private readonly Api\GetBankOffers\Factory $getBankOffersFactory,
        private readonly Api\GetBusinessOffers\Command $getBusinessOffersCommand,
        private readonly Api\GetBusinessOffers\Factory $getBusinessOffersFactory,
        private readonly Api\ProcessAdditionalInformation\Command $processAdditionalInformationCommand,
        private readonly Api\ProcessAdditionalInformation\Factory $processAdditionalInformationFactory,
        private readonly Api\ProcessBankOffers\Command $processBankOffersCommand,
        private readonly Api\ProcessBankOffers\Factory $processBankOffersFactory,
        private readonly Api\ProcessBusinessOffers\Command $processBusinessOffersCommand,
        private readonly Api\ProcessBusinessOffers\Factory $processBusinessOffersFactory,
        private readonly Api\GetAdverts\Command $getAdvertsCommand,
        private readonly Api\GetAdverts\Factory $getAdvertsFactory,
        private readonly Api\ProcessAdverts\Command $processAdvertsCommand,
        private readonly Api\ProcessAdverts\Factory $processAdvertsFactory,
        private readonly Api\SkipAdverts\Command $skipAdvertsCommand,
        private readonly Api\SkipAdverts\Factory $skipAdvertsFactory,
    ) {
        $this->setApikeyRequiredFalse();
    }

    public function buildAdditionalInformation(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->buildAdditionalInformationCommand->execute(
                    $this->buildAdditionalInformationFactory->makeRequest(
                        $company,
                    )
                )
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function getAdverts(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->getAdvertsCommand->execute(
                    $this->getAdvertsFactory->makeRequest(
                        $company,
                    )
                )
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function getBankOffers(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->getBankOffersCommand->execute(
                    $this->getBankOffersFactory->makeRequest(
                        $company,
                    )
                )
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function getBusinessOffers(Company $company, ?int $categoryId = null): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->getBusinessOffersCommand->execute(
                    $this->getBusinessOffersFactory->makeRequest(
                        $company,
                        $categoryId > 0 ? $categoryId : null
                    )
                )
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function processAdditionalInformation(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->processAdditionalInformationCommand->execute(
                    $this->processAdditionalInformationFactory->makeRequest(
                        $company,
                        $this->getSymfonyRequest()->getPayload()->all()
                    )
                )
            );
        } catch (InvalidInformation|\InvalidArgumentException $e) {
            return $this->errorResponse($e, logError: false);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function processAdverts(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->processAdvertsCommand->execute(
                    $this->processAdvertsFactory->makeRequest(
                        $company,
                        $this->getSymfonyRequest()->getPayload()->all()
                    )
                )
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function skipAdverts(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->skipAdvertsCommand->execute(
                    $this->skipAdvertsFactory->makeRequest(
                        $company
                    )
                )
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function processBankOffers(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->processBankOffersCommand->execute(
                    $this->processBankOffersFactory->makeRequest(
                        $company,
                        $this->getSymfonyRequest()->getPayload()->all(),
                    )
                ),
                Response::HTTP_CREATED
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function processBusinessOffers(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->processBusinessOffersCommand->execute(
                    $this->processBusinessOffersFactory->makeRequest(
                        $company,
                        $this->getSymfonyRequest()->getPayload()->all(),
                    )
                ),
                Response::HTTP_CREATED
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }
}
