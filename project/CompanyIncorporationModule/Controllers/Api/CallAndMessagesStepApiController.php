<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\Controllers\Api;

use CompanyIncorporationModule\Exceptions\CallAndMessages\CompanyHasReservedNumberException;
use CompanyIncorporationModule\UseCase\CallsAndMessages\Api;
use Entities\Company;
use RouterModule\ApiController;
use Symfony\Component\HttpFoundation\JsonResponse;

class CallAndMessagesStepApiController extends ApiController
{
    public function __construct(
        private readonly Api\GetMoneyPennyNumbers\Command $getMoneyPennyNumbersCommand,
        private readonly Api\GetMoneyPennyNumbers\Factory $getMoneyPennyNumbersFactory,
        private readonly Api\ProcessMoneyPennyNumbers\Command $processMoneyPennyNumbersCommand,
        private readonly Api\ProcessMoneyPennyNumbers\Factory $processMoneyPennyNumbersFactory,
        private readonly Api\RetrieveMoneyPennyNumbers\Command $retrieveMoneyPennyNumbersCommand,
        private readonly Api\RetrieveMoneyPennyNumbers\Factory $retrieveMoneyPennyNumbersFactory,
    ) {
        $this->setApikeyRequiredFalse();
    }

    public function getMoneyPennyNumbers(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->getMoneyPennyNumbersCommand->execute(
                    $this->getMoneyPennyNumbersFactory->makeRequest(
                        $company,
                    )
                )
            );
        } catch (\Throwable $e) {
            return $this->apiErrorResponse($e->getMessage(), [], $e->getCode());
        }
    }

    public function processMoneyPennyNumbers(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->processMoneyPennyNumbersCommand->execute(
                    $this->processMoneyPennyNumbersFactory->makeRequest(
                        $company,
                        $this->getSymfonyRequest()->getPayload()->all(),
                    )
                )
            );
        } catch (CompanyHasReservedNumberException $e) {
            return $this->apiErrorResponse($e->getMessage(), [], $e->getCode(), logError: false);
        } catch (\Throwable $e) {
            return $this->apiErrorResponse($e->getMessage(), [], $e->getCode());
        }
    }

    public function retrieveMoneyPennyNumbers(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->retrieveMoneyPennyNumbersCommand->execute(
                    $this->retrieveMoneyPennyNumbersFactory->makeRequest(
                        $company,
                        $this->getSymfonyRequest()->getPayload()->all(),
                    )
                )
            );
        } catch (\InvalidArgumentException $e) {
            return $this->apiErrorResponse($e->getMessage(), [], $e->getCode(), logError: false);
        } catch (\Throwable $e) {
            return $this->apiErrorResponse($e->getMessage(), [], $e->getCode());
        }
    }
}
