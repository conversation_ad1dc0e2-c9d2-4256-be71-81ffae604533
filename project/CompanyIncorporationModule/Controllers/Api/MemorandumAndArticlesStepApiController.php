<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\Controllers\Api;

use CompanyIncorporationModule\UseCase\MemorandumAndArticles\Api;
use Entities\Company;
use RouterModule\ApiController;
use Symfony\Component\HttpFoundation\JsonResponse;

class MemorandumAndArticlesStepApiController extends ApiController
{
    public function __construct(
        private readonly Api\DownloadArticle\Command $downloadArticleCommand,
        private readonly Api\DownloadArticle\Factory $downloadArticleFactory,
        private readonly Api\GenerateDefaultArticle\Command $generateDefaultArticleCommand,
        private readonly Api\GenerateDefaultArticle\Factory $generateDefaultArticleFactory,
        private readonly Api\GetArticles\Command $getArticlesCommand,
        private readonly Api\GetArticles\Factory $getArticlesFactory,
        private readonly Api\RemoveArticle\Command $removeArticleCommand,
        private readonly Api\RemoveArticle\Factory $removeArticleFactory,
        private readonly Api\UploadArticle\Command $uploadArticleCommand,
        private readonly Api\UploadArticle\Factory $uploadArticleFactory,
    ) {
        $this->setApikeyRequiredFalse();
    }

    public function getArticles(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->getArticlesCommand->execute(
                    $this->getArticlesFactory->makeRequest($company)
                )
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function uploadArticle(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->uploadArticleCommand->execute(
                    $this->uploadArticleFactory->makeRequest(
                        $company,
                        $this->getSymfonyRequest()->getPayload()->all(),
                    )
                )
            );
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e, logError: false);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function generateDefaultArticle(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->generateDefaultArticleCommand->execute(
                    $this->generateDefaultArticleFactory->makeRequest($company)
                )
            );
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e, logError: false);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function removeArticle(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->removeArticleCommand->execute(
                    $this->removeArticleFactory->makeRequest(
                        $company,
                        $this->getSymfonyRequest()->getPayload()->all(),
                    )
                )
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function downloadArticle(Company $company, int $articleId): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->downloadArticleCommand->execute(
                    $this->downloadArticleFactory->makeRequest(
                        $company,
                        $articleId,
                    )
                )
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }
}
