<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\Controllers\Api;

use CompaniesHouseModule\Entities\Address;
use CompanyFormationModule\Dto\PscsData;
use CompanyFormationModule\Forms\PscsForm;
use CompanyFormationModule\Providers\ControllerParametersProvider;
use CompanyFormationModule\Views\PscsOverviewViewFactory;
use CompanyIncorporationModule\Dto\ConsolidatedMember;
use CompanyIncorporationModule\Exceptions\ExistingMemberException;
use CompanyIncorporationModule\Exceptions\InvalidNatureOfControlException;
use CompanyIncorporationModule\Facades\AppointmentsFacade;
use CompanyIncorporationModule\Helpers\MemberHashHelper;
use CompanyIncorporationModule\Services\MemberService;
use CompanyIncorporationModule\Services\RegisteredOfficeService;
use CompanyIncorporationModule\Services\ShareholderService;
use CompanyIncorporationModule\UseCase\Appointments\Api;
use Entities\Company;
use Exceptions\Business\CompanyException;
use JMS\Serializer\Exception\Exception as JMSSerializerException;
use Models\Products\Product;
use PaymentModule\Factories\InlinePaymentFactory;
use PaymentModule\Views\InlinePayment\InlinePayment;
use RouterModule\ApiController;
use SerializingModule\Serializer;
use Services\CompanyService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use UserModule\Services\ICustomerAvailability;

class AppointmentsStepApiController extends ApiController
{
    public function __construct(
        private readonly AppointmentsFacade $appointmentsFacade,
        private readonly MemberHashHelper $memberHashHelper,
        private readonly MemberService $memberService,
        private readonly Serializer $serializer,
        private readonly RegisteredOfficeService $registeredOfficeService,
        private readonly InlinePaymentFactory $inlinePaymentFactory,
        private readonly ControllerParametersProvider $parametersProvider,
        private readonly ShareholderService $shareholderService,
        private readonly CompanyService $companyService,
        private readonly ICustomerAvailability $customerAvailability,
        private readonly PscsOverviewViewFactory $pscsOverviewViewFactory,
        private readonly Api\SetNoPscReason\Command $setNoPscReasonCommand,
        private readonly Api\SetNoPscReason\Factory $setNoPscReasonFactory,
    ) {
        $this->setApikeyRequiredFalse();
    }

    /**
     * @throws JMSSerializerException
     * @throws \Exception
     */
    public function addMember(Company $company, ?string $memberData = null): JsonResponse
    {
        try {
            $parsedMemberData = json_decode($memberData, true);
            $consolidatedMember = $this->appointmentsFacade->saveMember($company, $parsedMemberData);

            $personType = $parsedMemberData['isCorporate'] ? ConsolidatedMember::TYPE_CORPORATE : ConsolidatedMember::TYPE_PERSON;
            $companyCategory = $company->getCompanyCategory();
            $consolidatedMemberInfo = $this->memberService->getConsolidatedMemberInfo(
                $consolidatedMember,
                $personType,
                $companyCategory
            );

            return $this->apiSuccessResponse([
                'message' => 'Members added successfully.',
                'membersHash' => $this->memberHashHelper->getMemberHash($consolidatedMember),
                'details' => $this->serializer->toArray($consolidatedMemberInfo['details']),
                'dob' => $consolidatedMemberInfo['dob'],
                'roles' => $consolidatedMemberInfo['roles'],
                'authentication' => $consolidatedMemberInfo['authentication'],
                'isOfficerComplete' => !$this->memberService->isOfficerIncomplete($consolidatedMember, $company),
            ]);
        } catch (ExistingMemberException $e) {
            return $this->errorResponse(exception: $e, logError: false);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function deleteMember(Company $company, ConsolidatedMember $consolidatedMember, ?string $rolesToDelete = ''): JsonResponse
    {
        try {
            $this->appointmentsFacade->deleteMember(
                $company,
                $consolidatedMember,
                json_decode($rolesToDelete, true)
            );

            return $this->apiSuccessResponse(['message' => 'Member removed successfully.']);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    /**
     * @throws \Exception
     */
    public function saveMemberDetails(Company $company, ConsolidatedMember $consolidatedMember, ?string $memberData = null): JsonResponse
    {
        try {
            return $this->apiSuccessResponse([
                'message' => 'Member Details saved successfully.',
                'authentication' => $this->appointmentsFacade->saveMemberDetails(
                    $company,
                    $consolidatedMember,
                    json_decode($memberData, true)
                ),
                'isOfficerComplete' => !$this->memberService->isOfficerIncomplete($consolidatedMember, $company),
            ]);
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse(exception: $e, logError: false);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function retrieveAddresses(Company $company): JsonResponse
    {
        try {
            $hasAddressUpsellOffer = $this->registeredOfficeService->canShowUpsellOffer($company);
            $incorporation = $company->getIncorporationFormSubmission();

            /** @var Product $fullPrivacyPackageMonthly */
            $fullPrivacyPackageMonthly = $this->registeredOfficeService->getFullPrivacyPackageMonthly();
            $registeredOfficeUpsellType = $company->getSettings()->getRegisteredOfficeUpsell()->getType()
                ?: $fullPrivacyPackageMonthly->getId();
            $prefillAddresses = $this->parametersProvider->getPrefillAddresses($company);
            $inlinePayment = $this->inlinePaymentFactory->create(
                $fullPrivacyPackageMonthly,
                $company,
                ['retryWithANewCard' => true]
            );

            return $this->apiSuccessResponse(
                [
                    'hasAddressUpsellOffer' => $hasAddressUpsellOffer,
                    'registeredOfficeUpsellType' => $registeredOfficeUpsellType,
                    'prefillAddresses' => $prefillAddresses,
                    'offerData' => [
                        'omnipayUrl' => $inlinePayment->getOmnipayUrl(),
                        'omnipayComponentData' => $inlinePayment->getOmnipayComponentData(),
                        'paymentStatus' => $inlinePayment->getStatus(),
                        'product' => $inlinePayment->getItem(),
                    ],
                    'hasServiceAddressIncluded' => !empty($company->getRegisteredOfficeId()),
                    'msgAddress' => $this->registeredOfficeService->getMSGAddress(),
                    'isUsingMsgAddress' => $incorporation->getMsgAddress(),
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function saveAddresses(Company $company, string $memberHash, string $addressData): JsonResponse
    {
        try {
            $errors = [];
            $data = json_decode($addressData, true);

            if (empty($data['serviceAddress'])) {
                return $this->apiErrorResponse(error: 'The serviceAddress address is mandatory.', logError: false);
            }

            $residentialAddress = new Address();
            if (!empty($data['residentialAddress'])) {
                $residentialAddress = new Address(
                    premise: $data['residentialAddress']['premise'],
                    street: $data['residentialAddress']['street'],
                    postTown: $data['residentialAddress']['postTown'],
                    postcode: $data['residentialAddress']['postcode'],
                    country: $data['residentialAddress']['country'],
                    thoroughfare: $data['residentialAddress']['thoroughfare'] ?? null,
                    county: $data['residentialAddress']['county'] ?? null,
                );
                if ($data['isResidentialAddressRequired']) {
                    $errors['residentialAddress'] = $this->appointmentsFacade->validateAddress($residentialAddress);
                }
            }

            $serviceAddress = new Address(
                premise: $data['serviceAddress']['premise'],
                street: $data['serviceAddress']['street'],
                postTown: $data['serviceAddress']['postTown'],
                postcode: $data['serviceAddress']['postcode'],
                country: $data['serviceAddress']['country'],
                thoroughfare: $data['serviceAddress']['thoroughfare'] ?? null,
                county: $data['serviceAddress']['county'] ?? null,
            );

            $errors['serviceAddress'] = $this->appointmentsFacade->validateAddress($serviceAddress);

            if (!empty($errors['serviceAddress'])) {
                throw new \InvalidArgumentException(sprintf('Service Address (%s): %s', array_key_first($errors['serviceAddress']), $errors['serviceAddress'][array_key_first($errors['serviceAddress'])][0]), Response::HTTP_BAD_REQUEST);
            }
            if (!empty($errors['residentialAddress'])) {
                throw new \InvalidArgumentException(sprintf('Residential Address (%s): %s', array_key_first($errors['residentialAddress']), $errors['residentialAddress'][array_key_first($errors['residentialAddress'])][0]), Response::HTTP_BAD_REQUEST);
            }

            if ($data['isResidentialAddressRequired'] && empty($data['residentialAddress'])) {
                $residentialAddress = $serviceAddress;
            }

            $consolidatedMember = $this->memberHashHelper->getConsolidatedMember($memberHash);
            $this->appointmentsFacade->saveOfficerAddresses(
                $consolidatedMember->getMembers(),
                $serviceAddress,
                $residentialAddress
            );

            return $this->apiSuccessResponse([
                'message' => 'Addresses saved successfully.',
                'serviceAddress' => $serviceAddress->jsonSerialize(),
                'residentialAddress' => $residentialAddress->jsonSerialize(),
                'isUsingMsgAddress' => $this->memberService->isOurPostCode($serviceAddress->getPostcode()),
                'isOfficerComplete' => !$this->memberService->isOfficerIncomplete($consolidatedMember, $company),
            ]);
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse(exception: $e, logError: false);
        } catch (\Throwable $e) {
            return $this->errorResponse($e, $errors);
        }
    }

    public function saveShareholderInformation(string $companyId, string $memberHash, string $shareholderData): JsonResponse
    {
        try {
            if (!$companyId || !$memberHash || !$shareholderData) {
                throw new \InvalidArgumentException('missing mandatory data', Response::HTTP_BAD_REQUEST);
            }

            $company = $this->getCompany($companyId);
            if (!$company) {
                throw new \InvalidArgumentException('Company or customer not found', Response::HTTP_BAD_REQUEST);
            }

            $data = json_decode($shareholderData, true);
            if (empty($data['authentication']) || empty($data['allotmentOfShares'])) {
                throw new \InvalidArgumentException('Authentication and allotment of shares are mandatory', Response::HTTP_BAD_REQUEST);
            }

            $member = $this->shareholderService->saveShareholderData($company, $this->memberHashHelper->getConsolidatedMember($memberHash), $data);

            $consolidatedMembers = $this->memberService->getConsolidatedMembers($company);

            foreach ($consolidatedMembers as $consolidatedMember) {
                foreach ($consolidatedMember->getShareholders() as $shareholder) {
                    if ($shareholder->getId() === $member->getShareholders()[0]->getId()) {
                        $member = $consolidatedMember;
                        break;
                    }
                }
            }

            return $this->apiSuccessResponse([
                'message' => 'Shareholder info updated successfully',
                'membersHash' => $this->memberHashHelper->getMemberHash($member),
                'data' => $data,
            ]);
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse(exception: $e, logError: false);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function getPscPackageInfo(Company $company, ConsolidatedMember $consolidatedMember): JsonResponse
    {
        try {
            $pscsOverviewView = $this->pscsOverviewViewFactory->createView($company);

            return $this->apiSuccessResponse([
                'pscIsUpsellEligible' => $pscsOverviewView->isUpsellEligible(),
                'pscRegisterProductOwned' => $pscsOverviewView->getInlinePaymentStatus() === InlinePayment::STATUS_INIT
                    ? PscsForm::TYPE_SELF_PSC_REGISTER
                    : PscsForm::TYPE_MSG_PSC_REGISTER,
                'pscRegisterProduct' => Product::PRODUCT_PSC_REGISTER_UPSELL,
                'hasNoPscReason' => $pscsOverviewView->hasNoPscReason(),
                'omnipayUrl' => $pscsOverviewView->getInlinePayment()->getOmnipayUrl(),
                'omnipayData' => $pscsOverviewView->getInlinePayment()->getOmnipayComponentData(),
            ]);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function saveNatureOfControl(Company $company, ConsolidatedMember $consolidatedMember, ?string $natureOfControlData = null): JsonResponse
    {
        try {
            $pscsOverviewView = $this->pscsOverviewViewFactory->createView($company);

            $pscsData = new PscsData();
            $pscsData->setType($pscsOverviewView->getInlinePaymentStatus() === InlinePayment::STATUS_INIT
                ? (string) PscsForm::TYPE_SELF_PSC_REGISTER
                : (string) PscsForm::TYPE_MSG_PSC_REGISTER
            );

            $natureOfControl = $this->appointmentsFacade->saveNatureOfControl(
                $company,
                $consolidatedMember,
                json_decode($natureOfControlData, true),
                $pscsData
            );

            return $this->apiSuccessResponse([
                'message' => 'Nature of control saved successfully.',
                'natureOfControl' => $this->serializer->serialize($natureOfControl, 'json'),
            ]);
        } catch (InvalidNatureOfControlException $e) {
            return $this->errorResponse(exception: $e, logError: false);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function getPrefillMemberDetailsFromCustomerOtherCompanies(Company $company, string $memberName, bool $isCorporate): JsonResponse
    {
        try {
            if (empty($memberName)) {
                throw new \InvalidArgumentException('Member name is mandatory', Response::HTTP_BAD_REQUEST);
            }

            $memberDetails = $this->memberService->getMemberDetailsFromCustomerOtherCompanies($company, $memberName, $isCorporate);

            return $this->apiSuccessResponse(
                ['membersDetails' => $this->serializer->serialize($memberDetails, 'json')]
            );
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse(exception: $e, logError: false);
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    public function setNoPscReason(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->setNoPscReasonCommand->execute(
                    $this->setNoPscReasonFactory->makeRequest($company)
                )
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e);
        }
    }

    /**
     * @throws CompanyException
     */
    private function getCompany(string $companyId): ?Company
    {
        $customer = $this->customerAvailability->optionalLoggedInCustomer();

        if (!$customer) {
            return null;
        }

        return $this->companyService->getCustomerCompany($customer, $companyId);
    }
}
