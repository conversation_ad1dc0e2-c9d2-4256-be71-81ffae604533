<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\Controllers\Api;

use CompanyIncorporationModule\Exceptions\Summary\SummaryLawfulPurposeStatementMissingException;
use CompanyIncorporationModule\UseCase\Summary\Api;
use Entities\Company;
use MoneyPennyNumbersModule\Exceptions\MoneyPennyNumbersException;
use RouterModule\ApiController;
use Symfony\Component\HttpFoundation\JsonResponse;

class SummaryStepApiController extends ApiController
{
    public function __construct(
        private readonly Api\SubmitReview\Command $submitReviewCommand,
        private readonly Api\SubmitReview\Factory $submitReviewFactory,
        private readonly Api\SubmitIncorporation\Command $submitIncorporationCommand,
        private readonly Api\SubmitIncorporation\Factory $submitIncorporationFactory,
    ) {
        $this->setApikeyRequiredFalse();
    }

    public function submitReview(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->submitReviewCommand->execute(
                    $this->submitReviewFactory->makeRequest(
                        $company,
                        $this->getSymfonyRequest()->getPayload()->all(),
                    )
                )
            );
        } catch (SummaryLawfulPurposeStatementMissingException|MoneyPennyNumbersException $e) {
            return $this->apiErrorResponse($e->getMessage(), [], $e->getCode(), logError: false);
        } catch (\Throwable $e) {
            return $this->apiErrorResponse($e->getMessage(), [], $e->getCode());
        }
    }

    public function submitIncorporation(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->submitIncorporationCommand->execute(
                    $this->submitIncorporationFactory->makeRequest(
                        $company,
                        $this->getSymfonyRequest()->getPayload()->all(),
                    )
                )
            );
        } catch (SummaryLawfulPurposeStatementMissingException|MoneyPennyNumbersException $e) {
            return $this->apiErrorResponse($e->getMessage(), [], $e->getCode(), logError: false);
        } catch (\Throwable $e) {
            return $this->apiErrorResponse($e->getMessage(), [], $e->getCode());
        }
    }
}
