<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\Controllers;

use CompanyFormationModule\Deciders\RegistrationReviewDecider;
use CompanyFormationModule\Providers\StepProvider;
use CompanyFormationModule\Services\ShareholderService;
use CompanyIncorporationModule\Dto\Enum\Step;
use CompanyIncorporationModule\Dto\Enum\SubStep;
use CompanyIncorporationModule\Services\IncorporationProcessLogService;
use CompanyIncorporationModule\Services\MemorandumAndArticles\MemorandumAndArticlesService;
use CompanyIncorporationModule\Services\StepService;
use CompanyIncorporationModule\Services\StepUrlService;
use Entities\Company;
use JMS\Serializer\Exception\Exception as JMSSerializerException;
use Models\OldModels\ReservedWord;
use SerializingModule\Serializer;
use Services\EventService;
use Symfony\Component\HttpFoundation\Response;
use TemplateModule\Renderers\IRenderer;

readonly class MemorandumAndArticlesStepController
{
    public const MEMORANDUM_AND_ARTICLES_VIEWED_EVENT = 'company_formation.memorandum_and_articles_viewed';
    public const MEMORANDUM_AND_ARTICLES_FILLED_EVENT = 'company_incorporation.memorandum_and_articles_filled';
    public const FILE_SIZE_10_MB = 10485760;
    public const FILE_MAX_LENGTH = 1200000;

    public function __construct(
        private EventService $eventService,
        private IRenderer $renderer,
        private Serializer $serializer,
        private StepService $stepService,
        private StepUrlService $stepUrlService,
        private ShareholderService $shareholderService,
        private MemorandumAndArticlesService $memorandumAndArticlesService,
        private IncorporationProcessLogService $incorporationProcessLogService,
        private RegistrationReviewDecider $registrationReviewDecider,
    ) {
    }

    /**
     * @throws JMSSerializerException
     * @throws \Exception
     */
    public function buildMemorandumAndArticles(Company $company): Response
    {
        $this->eventService->notifyPreventDuplicationCached(self::MEMORANDUM_AND_ARTICLES_VIEWED_EVENT, $company->getId());
        $renderData = $this->memorandumAndArticlesService->getMemorandumAndArticlesRenderData($company);
        $stepData = $this->stepService->getStepData($company, StepProvider::STEP_MEMORANDUM_AND_ARTICLES);

        $renderData = [
            'company' => $this->getSerializedCompanyData($company),
            'tabSteps' => $this->serializer->serialize($stepData['tabSteps'], 'json'),
            'reservedWords' => $this->serializer->serialize($this->parseReservedWords(
                ReservedWord::match($company->getCompanyName(), (string) ReservedWord::TYPE_TEMPLATE),
            )),
            'hasMultipleShareClasses' => $this->serializer->serialize(
                $this->shareholderService->hasMultipleShareClasses($company),
                'json'
            ),
            'hasCustomArticles' => $this->serializer->serialize($renderData->hasCustomArticles(), 'json'),
            'customMemorandumAndArticles' => $this->serializeData($renderData->getCustomMemorandumAndArticles()),
            'memorandumAndArticles' => $this->serializeData($renderData->getMemorandumAndArticles()),
            'supportingDocument' => $this->serializeData($renderData->getSupportingDocument()),
            'maxFileSize' => self::FILE_SIZE_10_MB,
            'changeNameUrl' => $this->stepUrlService->getCompanyNameUrl(
                $company->getId(),
                $this->stepUrlService->getMemorandumAndArticlesUrl($company->getId())
            ),
            'serializedCurrentStep' => $this->stepService->getSerializedCurrentStep(
                $this->stepService->getTabs($company, StepProvider::STEP_MEMORANDUM_AND_ARTICLES),
                StepProvider::STEP_MEMORANDUM_AND_ARTICLES
            ),
            'seo' => ['title' => 'Memorandum & Articles'],
            'isUnderReview' => $this->registrationReviewDecider->isInReviewQueue($company),
        ];

        $this->incorporationProcessLogService->create(
            $company,
            true,
            Step::COMPANY_INCORPORATION_MEMORANDUM_AND_ARTICLES,
            SubStep::NO_SUB_STEP,
            $this->stepUrlService->getMemorandumAndArticlesUrl($company->getId()),
            $renderData
        );

        return $this->renderer->render($renderData);
    }

    /**
     * @throws JMSSerializerException
     */
    private function getSerializedCompanyData(Company $company): string
    {
        return $this->serializer->serialize([
            'id' => $company->getId(),
            'name' => $company->getName()->jsonSerialize(),
            'status' => $company->getStatus(),
            'category' => $company->getCompanyCategory(),
        ]);
    }

    private function parseReservedWords(array $reservedWords): array
    {
        return array_map(function (ReservedWord $reservedWord) {
            return [
                'filepath' => $reservedWord->file->getFilePath(true),
                'word' => $reservedWord->word,
            ];
        }, $reservedWords);
    }

    /**
     * @throws JMSSerializerException
     */
    private function serializeData(?array $data): ?string
    {
        if (empty($data)) {
            return null;
        }

        return $this->serializer->serialize($data, 'json');
    }
}
