<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\Controllers;

use CompanyFormationModule\Deciders\RegistrationReviewDecider;
use CompanyFormationModule\Providers\StepProvider;
use CompanyFormationModule\Repositories\SicCodesRepository;
use CompanyIncorporationModule\Dto\Enum\Step;
use CompanyIncorporationModule\Dto\Enum\SubStep;
use CompanyIncorporationModule\Services\BusinessBankingService;
use CompanyIncorporationModule\Services\IncorporationProcessLogService;
use CompanyIncorporationModule\Services\MailForwardingService;
use CompanyIncorporationModule\Services\RegisteredOfficeService;
use CompanyIncorporationModule\Services\StepService;
use CompanyIncorporationModule\Services\StepUrlService;
use Entities\Company;
use Entities\CompanyHouse\FormSubmission\CompanyIncorporation;
use FrontModule\controlers\CompaniesCustomerControler;
use Models\Products\Package;
use Models\Products\Product;
use OmnipayModule\Helpers\RequestHelper;
use PaymentModule\Factories\InlinePaymentFactory;
use Psr\Log\LoggerInterface;
use RouterModule\Generators\UrlGenerator;
use RouterModule\Helpers\IControllerHelper;
use RouterModule\Helpers\IMessageHelper;
use Symfony\Component\HttpFoundation\Response;
use TemplateModule\Renderers\IRenderer;
use Utils\Helpers\ArrayHelper;

readonly class CompanyFormationStepController
{
    public const COMPANY_FORMATION_PAGE = 'company_incorporation_module.company_formation';

    public const COMPANY_FORMATION_FILLED_EVENT = 'company_incorporation.company_formation_filled';

    /** @deprecated not being used, all packages are now eligible */
    public const PACKAGE_ITEMS = [
        Package::PACKAGE_BRONZE,
        Package::PACKAGE_BRONZE_PLUS,
        Package::PACKAGE_BASIC,
        Package::PACKAGE_CONTRACTOR,
        Package::PACKAGE_DIGITAL,
        Package::PACKAGE_PRINTED,
    ];
    public const SUB_STEP_BUSINESS_BANKING = 'BUSINESS_BANKING';
    public const SUB_STEP_INDUSTRY_TYPE = 'INDUSTRY_TYPE';

    public function __construct(
        private IRenderer $renderer,
        private UrlGenerator $urlGenerator,
        private IControllerHelper $controllerHelper,
        private InlinePaymentFactory $inlinePaymentFactory,
        private SicCodesRepository $sicCodesRepository,
        private StepService $stepService,
        private BusinessBankingService $businessBankingService,
        private MailForwardingService $mailForwardingService,
        private LoggerInterface $logger,
        private RegisteredOfficeService $registeredOfficeService,
        private StepUrlService $stepUrlService,
        private IncorporationProcessLogService $incorporationProcessLogService,
        private RegistrationReviewDecider $registrationReviewDecider,
    ) {
    }

    public function index(Company $company): Response
    {
        try {
            $backlink = $this->urlGenerator->url(
                self::COMPANY_FORMATION_PAGE,
                ['company' => $company->getId()]
            );

            try {
                $tabSteps = $this->stepService->getTabs($company);
                $step = $this->stepService->getStep($company, StepProvider::STEP_COMPANY_FORMATION);
                $currentSubStep = $this->stepService->getCurrentSubStep($step);
                $nextSubStep = $this->stepService->getNextSubStep($step);
                $subStepTitles = $step->getSubStepsTitles();
                $hasIndustryType = in_array(self::SUB_STEP_INDUSTRY_TYPE, $subStepTitles);
                $bankingAvailable = in_array(self::SUB_STEP_BUSINESS_BANKING, $subStepTitles);
            } catch (\Exception $e) {
                $this->logger->error(
                    'User unable to access Company Details page.',
                    ['exception' => $e]
                );

                return $this->controllerHelper->redirectionTo('homepage');
            }

            /** @var Product $fullPrivacyPackageMonthly */
            $fullPrivacyPackageMonthly = $this->registeredOfficeService->getFullPrivacyPackageMonthly();

            $inlinePayment = $this->inlinePaymentFactory->create(
                $fullPrivacyPackageMonthly,
                $company,
                ['retryWithANewCard' => true]
            );

            $incorporation = $company->getIncorporationFormSubmission();
            $companyAddress = $this->registeredOfficeService->getCompanyAddress($incorporation);

            $companyCodesWithDescription = $this->sicCodesRepository->getCompanySicCodesWithDesctiption($company);
            $mailForwardingOfferAvailable = $this->mailForwardingService->isOfferAvailable($company);
            $mailForwardingOfferStatus = $this->mailForwardingService->getOfferStatus($company);
            $bankingOfferData = $bankingAvailable
                ? $this->businessBankingService->getBankingOffers($company)
                : [];

            $offersData = ArrayHelper::get($bankingOfferData, 'offersData', null);
            $appliedBanks = ArrayHelper::get($bankingOfferData, 'appliedBanks', null);
            $selectedOffers = ArrayHelper::get($bankingOfferData, 'selectedOffers', null);
            $appliedBanksFromBusinessServices = ArrayHelper::get(
                $bankingOfferData,
                'appliedBanksFromBusinessServices',
                null
            );

            $mailForwardingSkipCtaLabel = $this->getMailForwardingSkipCtaLabel($company, $hasIndustryType);

            $mailForwardingContinueCtaLabel = $this->getMailForwardingContinueCtaLabel($company, $hasIndustryType);

            $industryTypeContinueButtonLabel = 'Submit & Continue';

            $registeredOfficeContinueButtonLabel = $mailForwardingOfferAvailable
                ? 'Continue to Mail Forwarding'
                : 'Continue to Nature of Business';

            $renderData = [
                'company'   => $company,
                'backLink'  => $backlink,
                'tabSteps'     => $tabSteps,
                'nextStep' => $nextSubStep->getTitle(),
                'currentStep' => $currentSubStep->getTitle(),
                'subSteps' => $subStepTitles,
                'inlinePayment' => $inlinePayment,
                'sicCodes' => $companyCodesWithDescription,
                'companyAddress' => $companyAddress,
                'bankingOfferData' => $offersData,
                'appliedBanks' => $appliedBanks,
                'appliedBanksFromBusinessServices' => $appliedBanksFromBusinessServices,
                'selectedOffers' => $selectedOffers,
                'isMailForwardingOfferAvailable' => $mailForwardingOfferAvailable,
                'mailForwardingOfferStatus' => $mailForwardingOfferStatus,
                'mailForwardingSkipCtaLabel' => $mailForwardingSkipCtaLabel,
                'mailForwardingContinueCtaLabel' => $mailForwardingContinueCtaLabel,
                'industryTypeContinueButtonLabel' => $industryTypeContinueButtonLabel,
                'registeredOfficeContinueButtonLabel' => $registeredOfficeContinueButtonLabel,
                'registeredOfficeCompleted' => $this->registeredOfficeService->isRegisteredOfficeCompleted($incorporation),
                'industryTypeCompleted' => $this->industryTypeCompleted($incorporation),
                'registeredEmailAddressSuggestion' => $company->getCustomer()->getEmail(),
                'serializedCurrentStep' => $this->stepService->getSerializedCurrentStep($tabSteps, StepProvider::STEP_COMPANY_FORMATION),
                'seo' => ['title' => 'Company Details'],
                'omnipayToken' => RequestHelper::generateToken('payment', (string) $company->getCustomer()->getId()),
                'omnipaySalt' => 'cms_customer_id',
                'omnipayUri' => 'payment',
                'isUnderReview' => $this->registrationReviewDecider->isInReviewQueue($company),
            ];

            $this->incorporationProcessLogService->create(
                $company,
                true,
                Step::COMPANY_INCORPORATION_COMPANY_FORMATION,
                SubStep::NO_SUB_STEP,
                $this->stepUrlService->getFormationStepUrl($company->getId()),
                $renderData
            );

            return $this->renderer->render($renderData);
        } catch (\Exception $e) {
            $this->logger->error('Unable to access the Company Details page', ['exception' => $e]);
            $this->controllerHelper->setFlashMessage($e->getMessage(), IMessageHelper::MESSAGE_ERROR);

            return $this->controllerHelper->redirectionTo(CompaniesCustomerControler::COMPANIES_PAGE);
        }
    }

    private function industryTypeCompleted(CompanyIncorporation $incorporation): bool
    {
        return !empty($incorporation->getSicCodes());
    }

    private function getMailForwardingSkipCtaLabel(Company $company, bool $hasIndustryType): string
    {
        if ($company->isLlpType()) {
            return 'Continue to Appointments';
        }

        return sprintf(
            'Skip %s',
            $hasIndustryType ? 'to Nature of Business' : 'to Business Banking'
        );
    }

    private function getMailForwardingContinueCtaLabel(Company $company, bool $hasIndustryType): string
    {
        if ($company->isLlpType()) {
            return 'Continue to Appointments';
        }

        return sprintf(
            'Continue %s',
            $hasIndustryType ? 'to Nature Business' : 'to Business Banking'
        );
    }
}
