<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\Controllers;

use BusinessServicesModule\AnnaModule\Facades\IncorporationFacade;
use CompanyFormationModule\Deciders\RegistrationReviewDecider;
use CompanyFormationModule\Factories\SummaryViewFactory;
use CompanyFormationModule\Providers\StepProvider;
use CompanyFormationModule\Services\ShareholderService;
use CompanyIncorporationModule\Dto\Enum\Step;
use CompanyIncorporationModule\Dto\Enum\SubStep;
use CompanyIncorporationModule\Services\IncorporationProcessLogService;
use CompanyIncorporationModule\Services\MemorandumAndArticles\MemorandumAndArticlesService;
use CompanyIncorporationModule\Services\StepService;
use CompanyIncorporationModule\Services\StepUrlService;
use CompanyIncorporationModule\Services\SummaryService;
use Entities\Company;
use FrontModule\controlers\CompaniesCustomerControler;
use IdModule\Repositories\IIdCompanyInfoRepository;
use JMS\Serializer\Exception\Exception as SerializerException;
use Models\OldModels\ReservedWord;
use Models\Products\Package;
use OmnipayModule\Helpers\RequestHelper;
use ProductModule\Repositories\ProductRepository;
use Psr\Log\LoggerInterface;
use RouterModule\Helpers\IControllerHelper;
use RouterModule\Helpers\IMessageHelper;
use SerializingModule\Serializer;
use Symfony\Component\HttpFoundation\Response;
use TemplateModule\Renderers\IRenderer;

readonly class SummaryStepController
{
    public function __construct(
        private IRenderer $renderer,
        private IncorporationFacade $annaIncorporationFacade,
        private StepService $stepService,
        private StepUrlService $stepUrlService,
        private LoggerInterface $logger,
        private IControllerHelper $controllerHelper,
        private SummaryViewFactory $summaryViewFactory,
        private SummaryService $summaryService,
        private IIdCompanyInfoRepository $idCompanyInfoRepository,
        private ShareholderService $shareholderService,
        private ProductRepository $productRepository,
        private MemorandumAndArticlesService $memorandumAndArticlesService,
        private IncorporationProcessLogService $incorporationProcessLogService,
        private Serializer $serializer,
        private RegistrationReviewDecider $registrationReviewDecider,
    ) {
    }

    /**
     * @throws SerializerException
     */
    public function index(Company $company, ?string $productId = null): Response
    {
        try {
            if (!$this->memorandumAndArticlesService->getMemorandumAndArticlesRenderData($company)->hasMemorandumAndArticles()) {
                $this->memorandumAndArticlesService->generateDefaultArticle($company);
            }

            $summaryInlineOffers = $this->productRepository->getSummaryInlineOffers();

            if ($productId && !array_key_exists($productId, $summaryInlineOffers)) {
                throw new \InvalidArgumentException('Provided product ID is invalid.');
            }

            $summaryView = $this->summaryViewFactory->create($company);

            if (ReservedWord::match($company->getCompanyName(), (string) ReservedWord::TYPE_TEMPLATE)
                && !$summaryView->getArticles()->hasSupportingDocument()) {
                return $this->controllerHelper->redirectionTo(
                    StepUrlService::MEMORANDUM_AND_ARTICLES_PAGE,
                    ['company' => $company->getId(), 'supportDocument' => true],
                );
            }

            if ($company->getProductId() === Package::PACKAGE_ANNA) {
                $this->annaIncorporationFacade->registerAnna($company);
            }

            $stepData = $this->stepService->getStepData($company, StepProvider::STEP_SUMMARY);
            $summaryStepUrl = $this->stepUrlService->getSummaryStepUrl($company->getId());
            $formSubmission = $company->getIncorporationFormSubmission();

            $renderData = [
                'company'        => $company->getSimplifiedCompanyData(),
                'companyDetails' => $summaryView->jsonSerialize(),
                'members'       => $this->summaryService->getMembersData($company),
                'companyId'     => $company->getId(),
                'tabSteps'      => $stepData['tabSteps'],
                'previousStepUrl' => $this->stepUrlService->getAppointmentsStepUrl($company->getId()),
                'backUrl' => $this->stepUrlService->getSummaryStepUrl($company->getId()),
                'companyNameUrl' => $this->stepUrlService->getCompanyNameUrl(
                    $company->getId(),
                    $summaryStepUrl
                ),
                'entitiesWithIdInvalid' => $this->idCompanyInfoRepository->getEntitiesWithInvalidIdSerialized($company),
                'entityIdChecksUrl' => $this->stepUrlService->getEntityIdChecksUrl($company->getId()),
                'appointmentsIdData' => $this->summaryService->getAppointmentsIdData($company),
                'hasMultipleShareClasses' => (string) $this->shareholderService->hasMultipleShareClasses($company),
                'productCHExtraFee' => $this->summaryService->getProductCHExtraFee($company),
                'productRegistrationReview' => $this->summaryService->getProductRegistrationReview($company),
                'inlinePaymentProductName' => $productId ? $summaryInlineOffers[$productId]->getName() : null,
                'serializedCurrentStep' => $this->stepService->getSerializedCurrentStep(
                    $this->stepService->getTabs($company),
                    StepProvider::STEP_SUMMARY
                ),
                'omnipayToken' => RequestHelper::generateToken('payment', (string) $company->getCustomer()->getId()),
                'omnipaySalt' => 'cms_customer_id',
                'omnipayUri' => 'payment',
                'hasReservedWord' => $this->hasReservedWord($company),
                'seo' => ['title' => 'Submission Review'],
                'isUnderReview' => $this->registrationReviewDecider->isInReviewQueue($company),
            ];

            $this->incorporationProcessLogService->create(
                $company,
                true,
                Step::COMPANY_INCORPORATION_SUMMARY,
                SubStep::NO_SUB_STEP,
                $summaryStepUrl,
                $renderData
            );

            return $this->renderer->render($renderData);
        } catch (\Exception $e) {
            $this->logger->error('Unable to access Summary page', ['exception' => $e]);
            $this->controllerHelper->setFlashMessage($e->getMessage(), IMessageHelper::MESSAGE_ERROR);

            return $this->controllerHelper->redirectionTo(CompaniesCustomerControler::COMPANIES_PAGE);
        }
    }

    /**
     * @throws SerializerException
     */
    private function hasReservedWord(Company $company): string
    {
        return $this->serializer->serialize(!empty(ReservedWord::match($company->getCompanyName(), (string) ReservedWord::TYPE_TEMPLATE)), 'json');
    }
}
