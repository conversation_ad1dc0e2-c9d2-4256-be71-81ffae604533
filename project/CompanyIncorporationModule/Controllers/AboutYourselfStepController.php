<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\Controllers;

use CompanyFormationModule\Deciders\RegistrationReviewDecider;
use CompanyFormationModule\Providers\StepProvider;
use CompanyIncorporationModule\Dto\Enum\Step;
use CompanyIncorporationModule\Dto\Enum\SubStep;
use CompanyIncorporationModule\Helpers\CustomerIdDetailsHelper;
use CompanyIncorporationModule\Services\IncorporationProcessLogService;
use CompanyIncorporationModule\Services\StepService;
use CompanyIncorporationModule\Services\StepUrlService;
use Entities\Company;
use Entities\Customer;
use FrontModule\controlers\CompaniesCustomerControler;
use Psr\Log\LoggerInterface;
use RouterModule\Helpers\IControllerHelper;
use RouterModule\Helpers\IMessageHelper;
use Services\EventService;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Intl\Countries;
use TemplateModule\Renderers\IRenderer;
use UserModule\Helpers\HowHeardOptions;
use UserModule\Helpers\IndustryOptions;

readonly class AboutYourselfStepController
{
    public const ABOUT_YOURSELF_FILLED_EVENT = 'company_incorporation.about_yourself_filled';
    private const EVENT_NAME = 'company_formation.about_yourself_viewed';

    public function __construct(
        private IRenderer $renderer,
        private IControllerHelper $controllerHelper,
        private LoggerInterface $logger,
        private EventService $eventService,
        private StepService $stepService,
        private StepUrlService $stepUrlService,
        private IncorporationProcessLogService $incorporationProcessLogService,
        private CustomerIdDetailsHelper $customerIdDetailsHelper,
        private RegistrationReviewDecider $registrationReviewDecider,
    ) {
    }

    public function index(Company $company): Response
    {
        try {
            $this->eventService->notifyPreventDuplicationCached(self::EVENT_NAME, $company->getId());

            $renderData = [
                'company' => $company->getSimplifiedCompanyData(),
                'customerDetails' => $this->customerIdDetailsHelper->fromCustomer($company->getCustomer()),
                'titles' => Customer::$titles,
                'countries' => Countries::getNames(),
                'industryChoices' => IndustryOptions::getIndustryOptions(),
                'howHeardChoices' => HowHeardOptions::getHowHeardOptions(),
                'tabSteps' => $this->stepService->getTabSteps($company),
                'serializedCurrentStep' => $this->stepService->getSerializedCurrentStep(
                    $this->stepService->getTabs($company),
                    StepProvider::STEP_ABOUT_YOURSELF
                ),
                'seo' => ['title' => 'Account Holder'],
                'isUnderReview' => $this->registrationReviewDecider->isInReviewQueue($company),
            ];

            $this->incorporationProcessLogService->create(
                $company,
                true,
                Step::COMPANY_INCORPORATION_ABOUT_YOURSELF,
                SubStep::NO_SUB_STEP,
                $this->stepUrlService->getAboutYourselfUrl($company->getId()),
                $renderData
            );

            return $this->renderer->render($renderData);
        } catch (\Throwable $e) {
            $this->logger->error('Unable to access the Account Holder Information page', ['exception' => $e]);
            $this->controllerHelper->setFlashMessage($e->getMessage(), IMessageHelper::MESSAGE_ERROR);

            return $this->controllerHelper->redirectionTo(CompaniesCustomerControler::COMPANIES_PAGE);
        }
    }
}
