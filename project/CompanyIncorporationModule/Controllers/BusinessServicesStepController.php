<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\Controllers;

use CompanyFormationModule\Deciders\RegistrationReviewDecider;
use CompanyFormationModule\Providers\StepProvider;
use CompanyIncorporationModule\Dto\Enum\Step;
use CompanyIncorporationModule\Dto\Enum\SubStep;
use CompanyIncorporationModule\Services\IncorporationProcessLogService;
use CompanyIncorporationModule\Services\StepService;
use CompanyIncorporationModule\Services\StepUrlService;
use Entities\Company;
use FrontModule\controlers\CompaniesCustomerControler;
use Psr\Log\LoggerInterface;
use RouterModule\Generators\UrlGenerator;
use RouterModule\Helpers\IControllerHelper;
use RouterModule\Helpers\IMessageHelper;
use SerializingModule\Serializer;
use Symfony\Component\HttpFoundation\Response;
use TemplateModule\Renderers\IRenderer;

class BusinessServicesStepController extends CompanyIncorporationAbstractController
{
    public const COMPANY_FORMATION_PAGE = 'company_incorporation_module.company_formation';
    public const BUSINESS_SERVICES_FILLED_EVENT = 'company_incorporation.business_services_filled';

    public function __construct(
        private readonly IRenderer $renderer,
        private readonly UrlGenerator $urlGenerator,
        private readonly IControllerHelper $controllerHelper,
        private readonly StepProvider $stepProvider,
        private readonly LoggerInterface $logger,
        private readonly Serializer $serializer,
        private readonly StepService $stepService,
        private readonly StepUrlService $stepUrlService,
        private readonly IncorporationProcessLogService $incorporationProcessLogService,
        private RegistrationReviewDecider $registrationReviewDecider,
    ) {
        parent::__construct($this->stepProvider, $this->logger, $this->controllerHelper);
    }

    public function index(Company $company): Response
    {
        try {
            $companyId = $company->getId();
            $stepData = $this->stepService->getStepData($company, StepProvider::STEP_BUSINESS_SERVICES);

            $renderData = [
                'company' => $company->getSimplifiedCompanyData(),
                'backLink' => $this->getCurrentStepLink($companyId),
                'tabSteps' => $this->serializer->serialize($stepData['tabSteps'], 'json'),
                'serializedCurrentStep' => $this->stepService->getSerializedCurrentStep(
                    $this->stepService->getTabs($company),
                    StepProvider::STEP_BUSINESS_SERVICES
                ),
                'seo' => ['title' => 'Business Services'],
                'isUnderReview' => $this->registrationReviewDecider->isInReviewQueue($company),
            ];

            $this->incorporationProcessLogService->create(
                $company,
                true,
                Step::COMPANY_INCORPORATION_BUSINESS_SERVICES,
                SubStep::NO_SUB_STEP,
                $this->stepUrlService->getBusinessServicesUrl($company->getId()),
                $renderData
            );

            return $this->renderer->render($renderData);
        } catch (\Throwable $e) {
            $this->logger->error('Unable to access the Company Details page', ['exception' => $e]);
            $this->controllerHelper->setFlashMessage($e->getMessage(), IMessageHelper::MESSAGE_ERROR);

            return $this->controllerHelper->redirectionTo(CompaniesCustomerControler::COMPANIES_PAGE);
        }
    }

    private function getCurrentStepLink(int $companyId): string
    {
        return $this->urlGenerator->url(self::COMPANY_FORMATION_PAGE,
            ['company' => $companyId]
        );
    }
}
