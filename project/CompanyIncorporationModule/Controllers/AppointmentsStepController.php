<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\Controllers;

use CompanyFormationModule\Controllers\FormationController;
use CompanyFormationModule\Deciders\RegistrationReviewDecider;
use CompanyFormationModule\Providers\ControllerParametersProvider;
use CompanyFormationModule\Providers\StepProvider;
use CompanyIncorporationModule\Deciders\NoPscReasonDecider;
use CompanyIncorporationModule\Dto\ConsolidatedMember;
use CompanyIncorporationModule\Dto\Enum\Step;
use CompanyIncorporationModule\Dto\Enum\SubStep;
use CompanyIncorporationModule\Exceptions\MemberNotFoundException;
use CompanyIncorporationModule\Exceptions\MemberNotImplementedException;
use CompanyIncorporationModule\Exceptions\MemberTypeMismatchException;
use CompanyIncorporationModule\Helpers\MemberHashHelper;
use CompanyIncorporationModule\Helpers\RolesHelper;
use CompanyIncorporationModule\Services\IncorporationProcessLogService;
use CompanyIncorporationModule\Services\MemberService;
use CompanyIncorporationModule\Services\PscService;
use CompanyIncorporationModule\Services\ShareholderService;
use CompanyIncorporationModule\Services\StepService;
use CompanyIncorporationModule\Services\StepUrlService;
use Entities\Company;
use Entities\Customer;
use JMS\Serializer\Exception\Exception as JMSSerializerException;
use Libs\CHFiling\Core\UtilityClass\Person;
use OmnipayModule\Helpers\RequestHelper;
use PeopleWithSignificantControl\Providers\PscChoicesProvider;
use Psr\Log\LoggerInterface;
use RouterModule\Helpers\IControllerHelper;
use SerializingModule\Context;
use SerializingModule\Serializer;
use Services\EventService;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use TemplateModule\Renderers\IRenderer;

readonly class AppointmentsStepController
{
    public const APPOINTMENTS_FILLED_EVENT = 'company_incorporation.appointments_filled';

    public const SUB_STEP_GENERAL_INFORMATION = 'general';
    public const SUB_STEP_MEMBER_DETAILS = 'memberDetails';
    public const SUB_STEP_ADDRESSES = 'addresses';
    public const SUB_STEP_MEMBER_INFORMATION = 'member';
    public const SUB_STEP_SHAREHOLDER_INFORMATION = 'shareholder';
    public const SUB_STEP_PSC = 'psc';

    public function __construct(
        private IRenderer $renderer,
        private StepService $stepService,
        private StepUrlService $stepUrlService,
        private MemberService $memberService,
        private Serializer $serializer,
        private ControllerParametersProvider $controllerParametersProvider,
        private MemberHashHelper $memberHashHelper,
        private ShareholderService $shareholderService,
        private PscChoicesProvider $pscChoicesProvider,
        private LoggerInterface $logger,
        private IControllerHelper $controllerHelper,
        private IncorporationProcessLogService $incorporationProcessLogService,
        private NoPscReasonDecider $noPscReasonDecider,
        private PscService $pscService,
        private EventService $eventService,
        private RegistrationReviewDecider $registrationReviewDecider,
    ) {
    }

    /**
     * @throws JMSSerializerException
     * @throws MemberNotFoundException
     * @throws MemberNotImplementedException
     */
    public function summary(Company $company): Response
    {
        $companyCategory = $company->getCompanyCategory();
        $consolidatedMembers = $this->memberService->getConsolidatedMembers($company);

        if (empty($consolidatedMembers)) {
            return $this->newMemberRedirect($company->getId());
        }

        if ($incompleteMember = $this->memberService->getMemberWithIncompleteAppointment($consolidatedMembers, $company)) {
            return $this->editMemberRedirect($incompleteMember, $company->getId());
        }

        $formattedMembers = $this->memberService->getFormattedMembers($company, $consolidatedMembers);

        $stepData = $this->stepService->getStepData($company, StepProvider::STEP_APPOINTMENTS);

        try {
            $tabSteps = $this->stepService->getTabs($company);
        } catch (\Exception $e) {
            $this->logger->error(
                'User unable to access Appointments page.',
                ['exception' => $e]
            );

            return $this->controllerHelper->redirectionTo('homepage');
        }

        $renderData = [
            'company' => $this->getSerializedCompanyData($company),
            'companyId'=> $company->getId(),
            'backUrl' => $this->stepUrlService->getAppointmentsStepUrl($company->getId()),
            'tabSteps' => $this->serializer->serialize($stepData['tabSteps'], 'json'),
            'members' => $formattedMembers,
            'addPersonUrl' => $this->stepUrlService->getNewMemberUrl($company->getId(), ConsolidatedMember::TYPE_PERSON),
            'roles' => $this->serializer->serialize(RolesHelper::getEmptyRoles($companyCategory), 'json'),
            'serializedCurrentStep' => $this->stepService->getSerializedCurrentStep($tabSteps, StepProvider::STEP_APPOINTMENTS),
            'showNoPscReason' => $this->noPscReasonDecider->canShowNoPscReason($company) ? 'true' : 'false',
            'noPscReason' => $company->getIncorporationFormSubmission()->getNoPscReason(),
            'seo' => ['title' => 'Appointments Summary'],
            'missingRequirements' => $this->memberService->getMissingRequirements($company),
            'isUnderReview' => $this->registrationReviewDecider->isInReviewQueue($company),
        ];

        $this->incorporationProcessLogService->create(
            $company,
            true,
            Step::COMPANY_INCORPORATION_APPOINTMENTS_SUMMARY,
            SubStep::NO_SUB_STEP,
            $this->stepUrlService->getAppointmentsStepUrl($company->getId()),
            $renderData
        );

        return $this->renderer->render($renderData);
    }

    /**
     * @throws JMSSerializerException
     * @throws \Exception
     */
    public function member(
        Company $company,
        ?string $personType = ConsolidatedMember::TYPE_PERSON,
        ?ConsolidatedMember $consolidatedMember = null,
    ): Response {
        $companyCategory = $company->getCompanyCategory();
        $stepData = $this->stepService->getStepData($company, StepProvider::STEP_APPOINTMENTS);

        try {
            $consolidatedMemberInfo = $this->memberService->getConsolidatedMemberInfo(
                $consolidatedMember,
                $personType,
                $companyCategory
            );
        } catch (MemberTypeMismatchException $e) {
            return $this->handleMemberTypeMismatchException($consolidatedMember, $company->getId());
        } catch (\Exception $e) {
            return $this->summaryRedirect($company->getId());
        }

        $this->eventService->notifyPreventDuplicationCached(FormationController::EVENT_FORMATION_BANKING_VIEWED, $company->getId());

        $hasAddressData = isset($consolidatedMemberInfo['serviceAddress']) && $consolidatedMemberInfo['serviceAddress']->getCountry() !== null;

        $serializedNatureOfControl = !empty($consolidatedMemberInfo['natureOfControl'])
            ? $this->serializer->serialize($consolidatedMemberInfo['natureOfControl'], 'json')
            : null;

        $serializedSuggestedNatureOfControl = null;
        $pscWarning = null;
        if (
            $this->memberService->hasAllAppointmentsCompleted($company)
            && $consolidatedMember
            && $suggestedNatureOfControl = $this->pscService->getSuggestedNatureOfControl($company, $consolidatedMember)
        ) {
            $serializedSuggestedNatureOfControl = $this->serializer->serialize($suggestedNatureOfControl, 'json');
            $pscWarning = $this->pscService->getWarning($company, $consolidatedMember);
        }

        $pscChoiceProviderType = $consolidatedMemberInfo['isCorporate'] ? PscChoicesProvider::CORPORATE : PscChoicesProvider::PERSON;

        try {
            $tabSteps = $this->stepService->getTabs($company);
        } catch (\Exception $e) {
            $this->logger->error(
                'User unable to access Appointments page.',
                ['exception' => $e]
            );

            return $this->controllerHelper->redirectionTo('homepage');
        }

        $renderData = [
            'company' => $this->getSerializedCompanyData($company),
            'companyId'=> $company->getId(),
            'backUrl' => $this->stepUrlService->getAppointmentsStepUrl($company->getId()),
            'subStepsData' => $this->serializer->serialize($stepData['subStepsData'], 'json'),
            'tabSteps' => $this->serializer->serialize($stepData['tabSteps'], 'json'),
            'previousStepUrl' => $this->stepUrlService->getFormationStepUrl($company->getId()),
            'personCountriesOfResidence' => $this->controllerParametersProvider->getPersonCountriesOfResidence(),
            'serviceAddressCountries' => $this->controllerParametersProvider->getServiceAddressCountries(),
            'residentialAddressCountries' => $this->controllerParametersProvider->getResidentialAddressCountries(),
            'nationalities' => $this->controllerParametersProvider->getNationalities(),
            'details' => $this->serializer->toArray($consolidatedMemberInfo['details']),
            'customerDetails' => $this->getSerializedCustomerDetails($company->getCustomer()),
            'hasOtherCompanies' => $company->getCustomer()->getCompanies()->count() > 1 ? 'true' : 'false',
            'dob' => $consolidatedMemberInfo['dob'],
            'isCorporate' => $consolidatedMemberInfo['isCorporate'] ? 'true' : 'false',
            'isOfficerComplete' => !$consolidatedMember || $this->memberService->isOfficerIncomplete($consolidatedMember, $company) ? 'false' : 'true',
            'consolidatedMemberHash' => $consolidatedMember
                ? $this->memberHashHelper->getMemberHash($consolidatedMember)
                : null,
            'roles' => $consolidatedMemberInfo['roles'],
            'consentToAct' => $consolidatedMemberInfo['consentToAct'] ? 'true' : 'false',
            'addPersonUrl' => $this->stepUrlService->getNewMemberUrl(
                $company->getId(),
                ConsolidatedMember::TYPE_PERSON
            ),
            'addCorporateUrl' => $this->stepUrlService->getNewMemberUrl(
                $company->getId(),
                ConsolidatedMember::TYPE_CORPORATE
            ),
            'addMemberApiUrl' => $this->stepUrlService->getAddMemberApiUrl($company->getId()),
            'titles' => Person::$titles,
            'authentication' => $consolidatedMemberInfo['authentication'],
            'authenticationSuggestion' => $this->memberService->getPreviousShareholderAuthentication($company)?->jsonSerialize(),
            'serviceAddress' => $consolidatedMemberInfo['serviceAddress'] !== null ? $this->serializer->serialize($consolidatedMemberInfo['serviceAddress'], 'json', new Context(skipIfNull: false)) : null,
            'residentialAddress' => $consolidatedMemberInfo['residentialAddress'] !== null ? $this->serializer->serialize($consolidatedMemberInfo['residentialAddress'], 'json', new Context(skipIfNull: false)) : null,
            'hasAddressData' => var_export($hasAddressData, true),
            'shareholderData' => $this->shareholderService->getShareholderData($company, $consolidatedMember),
            'pscWarning' => $pscWarning,
            'suggestedNatureOfControl' => $serializedSuggestedNatureOfControl,
            'natureOfControl' => $serializedNatureOfControl,
            'natureOfControlOptions' => $this->pscChoicesProvider->getNatureOfControlsTemplateStructure($pscChoiceProviderType, $company->getCompanyCategory()),
            'availableSubSteps' => $this->getAvailableSubSteps($company),
            'isUsingMsgAddress' => $consolidatedMemberInfo['isUsingMsgAddress'],
            'serializedCurrentStep' => $this->stepService->getSerializedCurrentStep($tabSteps, StepProvider::STEP_APPOINTMENTS),
            'companyHasShareholders' => $this->memberService->companyHasShareholders($company) ? 'true' : 'false',
            'companyHasMembers' => $this->memberService->companyHasMembers($company) ? 'true' : 'false',
            'seo' => ['title' => 'Appointments'],
            'omnipayToken' => RequestHelper::generateToken('payment', (string) $company->getCustomer()->getId()),
            'omnipaySalt' => 'cms_customer_id',
            'omnipayUri' => 'payment',
        ];

        $this->incorporationProcessLogService->create(
            $company,
            true,
            Step::COMPANY_INCORPORATION_APPOINTMENTS_MEMBER,
            SubStep::NO_SUB_STEP,
            $consolidatedMember
                ? $this->stepUrlService->getEditMemberUrl($company->getId(), $this->memberHashHelper->getMemberHash($consolidatedMember), $consolidatedMember->getPersonType())
                : $this->stepUrlService->getNewMemberUrl($company->getId(), ConsolidatedMember::TYPE_PERSON),
            $renderData
        );

        return $this->renderer->render($renderData);
    }

    /**
     * @throws JMSSerializerException
     */
    public function newMember(Company $company, ?string $personType = ConsolidatedMember::TYPE_PERSON): Response
    {
        return $this->member($company, $personType);
    }

    private function summaryRedirect(int $companyId): RedirectResponse
    {
        return new RedirectResponse(
            $this->stepUrlService->getAppointmentsStepUrl($companyId)
        );
    }

    private function newMemberRedirect(int $companyId): RedirectResponse
    {
        return new RedirectResponse(
            $this->stepUrlService->getNewMemberUrl($companyId, ConsolidatedMember::TYPE_PERSON)
        );
    }

    /**
     * @throws MemberNotFoundException
     */
    private function editMemberRedirect(ConsolidatedMember $consolidatedMember, int $companyId): RedirectResponse
    {
        return new RedirectResponse(
            $this->stepUrlService->getEditMemberUrl(
                $companyId,
                $this->memberHashHelper->getMemberHash($consolidatedMember),
                $consolidatedMember->getPersonType()
            )
        );
    }

    /**
     * @throws JMSSerializerException
     */
    private function getSerializedCompanyData(Company $company): string
    {
        return $this->serializer->serialize([
            'id' => $company->getId(),
            'name' => $company->getName()->jsonSerialize(),
            'status' => $company->getStatus(),
            'category' => $company->getCompanyCategory(),
        ]);
    }

    /**
     * @throws JMSSerializerException
     */
    private function getSerializedCustomerDetails(Customer $customer): string
    {
        return $this->serializer->serialize([
            'forename' => $customer->getFirstName(),
            'middleName' => $customer->getMiddleName(),
            'surname' => $customer->getLastName(),
            'dob' => $customer->getDateOfBirth()?->format('Y-m-d'),
            'title' => $customer->getTitle(),
        ]);
    }

    private function handleMemberTypeMismatchException(
        ConsolidatedMember $consolidatedMember,
        int $companyId,
    ): RedirectResponse {
        try {
            return $this->editMemberRedirect(
                $consolidatedMember,
                $companyId
            );
        } catch (MemberNotFoundException $e) {
            return $this->summaryRedirect($companyId);
        }
    }

    private function getAvailableSubSteps(Company $company): array
    {
        return match ($company->getCompanyCategory()) {
            Company::COMPANY_CATEGORY_LLP => [
                self::SUB_STEP_GENERAL_INFORMATION,
                self::SUB_STEP_MEMBER_DETAILS,
                self::SUB_STEP_ADDRESSES,
                self::SUB_STEP_PSC,
            ],
            Company::COMPANY_CATEGORY_BYGUAR => [
                self::SUB_STEP_GENERAL_INFORMATION,
                self::SUB_STEP_ADDRESSES,
                self::SUB_STEP_MEMBER_INFORMATION,
                self::SUB_STEP_PSC,
            ],
            default => [
                self::SUB_STEP_GENERAL_INFORMATION,
                self::SUB_STEP_ADDRESSES,
                self::SUB_STEP_SHAREHOLDER_INFORMATION,
                self::SUB_STEP_PSC,
            ],
        };
    }
}
