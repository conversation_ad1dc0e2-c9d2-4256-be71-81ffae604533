<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service class="CompaniesHouseModule\Controllers\Admin\DownloadChDataRequestController" id="companies_house_module.controllers.admin.download_ch_data_request_controller">
            <argument id="templating_module.renderers.admin_renderer" type="service" />
        </service>

        <service class="JMS\Serializer\SerializerBuilder" id="companies_house_module.serializer.builder">
            <factory class="CompaniesHouseModule\Factories\SerializerBuilderFactory" method="createWithUcFirstIdenticalNamingStrategy" />
        </service>

        <service class="CompaniesHouseModule\Helpers\PrefillHelper" id="companies_house_module.helpers.prefill_helper"/>

        <service id="companies_house_module.repositories.member_repository" class="CompaniesHouseModule\Repositories\MemberRepository">
            <factory service="doctrine.orm.entity_manager" method="getRepository" />
            <argument>CompaniesHouseModule\Entities\Member</argument>
        </service>

        <service id="companies_house_module.repositories.company_manager_repository" class="CompaniesHouseModule\Repositories\CompanyManagerRepository">
            <factory service="doctrine.orm.entity_manager" method="getRepository" />
            <argument>CompaniesHouseModule\Entities\CompanyManager</argument>
        </service>

        <service id="companies_house_module.repositories.form_submission_repository" class="CompaniesHouseModule\Repositories\FormSubmissionRepository">
            <factory service="doctrine.orm.entity_manager" method="getRepository" />
            <argument>Entities\CompanyHouse\FormSubmission</argument>
        </service>

        <service id="companies_house_module.repositories.company_incorporation_repository" class="CompaniesHouseModule\Repositories\CompanyIncorporationRepository">
            <factory service="doctrine.orm.entity_manager" method="getRepository" />
            <argument>Entities\CompanyHouse\FormSubmission\CompanyIncorporation</argument>
        </service>

        <service class="CompaniesHouseModule\Converters\MemberConverter" id="companies_house_module.converters.member_converter">
            <argument id="companies_house_module.services.member_service" type="service"/>
            <argument id="user_module.services.customer_availability" type="service"/>
            <tag name="router.argument_converter" supports="company_member"/>
        </service>

        <service class="CompaniesHouseModule\Converters\Admin\MemberConverter" id="companies_house_module.converters.admin.member_converter">
            <argument id="companies_house_module.services.member_service" type="service"/>
            <tag name="router.argument_converter" supports="admin.company_member"/>
        </service>

        <service class="CompaniesHouseModule\Services\MemberService" id="companies_house_module.services.member_service">
            <argument id="companies_house_module.repositories.member_repository" type="service"/>
        </service>

        <service class="CompaniesHouseModule\Commands\EnvelopeCleanerCommand" id="companies_house_module.commands.envelope_cleaner_command">
            <argument id="cron.loggers.default_logger" type="service"/>
            <argument id="dibi" type="service" />
            <argument>%companies_house.doc_path%</argument>
            <tag name="cron.command" command-name="clean:envelopes" action="cleanOldEnvelopes" />
        </service>

        <service class="CompaniesHouseModule\Validator\Constraints\ServiceAddressPostCodeValidator" id="companies_house_module.constraints.service_address_post_code_validator">
            <argument type="service" id="symfony.router"/>
            <tag name="validator.constraint_validator" alias="companies_house_module.forms.validators.constraints.service_address_post_code_validator" />
        </service>

        <service class="CompaniesHouseModule\FormSubmissions\BaseTypes\ServiceAddressFactory" id="companies_house_module.form_submissions.base_types.service_address_factory">
            <argument id="company_module.providers.our_service_address_provider" type="service"/>
        </service>

        <service class="CompaniesHouseModule\Views\PersonViewFactory" id="companies_house_module.views.person_view_factory" />

        <service class="CompaniesHouseModule\Helpers\Client" id="companies_house_module.helpers.client"/>

        <service class="CompaniesHouseModule\Factories\PersonalDetailsFactory" id="companies_house_module.factories.personal_details_factory"/>

        <service class="CompaniesHouseModule\Factories\CorporateDetailsFactory" id="companies_house_module.factories.corporate_details_factory"/>

        <service class="CompaniesHouseModule\Services\SubmissionHandler" id="companies_house_module.services.submission_handler">
            <argument id="id_module.repositories.id_validation_repository" type="service"/>
            <argument type="service" id="companies_house_module.repositories.id_submission_queue_repository"/>
            <argument type="service" id="companies_house_module.emailers.submission_queue_emailer"/>
            <argument type="service" id="symfony.event_dispatcher"/>
            <argument type="service" id="id_module.repositories.id_info_repository"/>
            <argument type="service" id="companies_house_module.deciders.ch_extra_fee_charge_decider"/>
            <argument type="service" id="id_module.repositories.entity_provider"/>
            <argument type="service" id="id_module.repositories.id_info_repository"/>
            <argument type="service" id="company_formation_module.repositories.form_submission_repository"/>
            <argument type="service" id="companies_house_module.repositories.review_submission_queue_repository"/>
        </service>

        <service class="CompaniesHouseModule\Factories\SubmissionQueueDatagridFactory" id="companies_house_module.factories.submission_queue_datagrid_factory">
            <argument id="id_module.verification.validation_checker" type="service"/>
            <argument id="companies_house_module.repositories.company_incorporation_repository" type="service"/>
            <argument id="templating_module.renderers.admin_renderer_simple" type="service"/>
        </service>

        <service class="CompaniesHouseModule\Controllers\Admin\SubmissionQueueController" id="companies_house_module.controllers.admin.submission_queue_controller">
            <argument id="companies_house_module.repositories.review_submission_queue_repository" type="service"/>
            <argument id="templating_module.renderers.admin_renderer" type="service"/>
            <argument id="companies_house_module.services.submission_handler" type="service"/>
            <argument id="router_module.helpers.controller_helper" type="service"/>
            <argument id="companies_house_module.factories.submission_queue_datagrid_factory" type="service"/>
            <argument id="services.submission_service" type="service"/>
            <argument id="companies_house_module.services.submission_review_service" type="service"/>
        </service>

        <service id="companies_house_module.repositories.submission_queue_repository" class="CompaniesHouseModule\Repositories\SubmissionQueueRepository">
            <factory service="doctrine.orm.entity_manager" method="getRepository" />
            <argument>CompaniesHouseModule\Entities\SubmissionQueue</argument>
        </service>

        <service id="companies_house_module.repositories.id_submission_queue_repository" class="CompaniesHouseModule\Repositories\IdSubmissionQueueRepository">
            <factory service="doctrine.orm.entity_manager" method="getRepository" />
            <argument>CompaniesHouseModule\Entities\IdSubmissionQueue</argument>
        </service>

        <service id="companies_house_module.repositories.review_submission_queue_repository" class="CompaniesHouseModule\Repositories\ReviewSubmissionQueueRepository">
            <factory service="doctrine.orm.entity_manager" method="getRepository" />
            <argument>CompaniesHouseModule\Entities\ReviewSubmissionQueue</argument>
        </service>

        <service class="CompaniesHouseModule\Emailers\SubmissionQueueEmailer" id="companies_house_module.emailers.submission_queue_emailer">
            <argument id="email_module.gateways.default_gateway" type="service"/>
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
        </service>

        <service class="CompaniesHouseModule\Commands\IncorporationErrorResubmissionCommand" id="companies_house_module.commands.incorporation_error_resubmission_command">
            <argument id="cron.loggers.default_logger" type="service"/>
            <argument id="services.company_service" type="service"/>
            <argument type="service" id="services.submission_service"/>
            <argument type="service" id="companies_house_module.updaters.legacy.submission_updater"/>
            <argument type="service" id="companies_house_module.repositories.legacy.envelope_repository"/>
            <tag name="cron.command" command-name="resubmit:incorporations" action="resubmit" />
        </service>

        <service class="CompaniesHouseModule\Updaters\Legacy\SubmissionUpdater" id="companies_house_module.updaters.legacy.submission_updater">
            <argument id="dibi" type="service"/>
        </service>

        <service class="CompaniesHouseModule\Repositories\Legacy\EnvelopeRepository" id="companies_house_module.repositories.legacy.envelope_repository">
            <argument id="dibi" type="service"/>
        </service>

        <service class="CompaniesHouseModule\Commands\SendSubmissionQueueRemindersCommand" id="companies_house_module.commands.send_submission_queue_reminders_command">
            <argument id="cron.loggers.default_logger" type="service"/>
            <argument id="companies_house_module.repositories.id_submission_queue_repository" type="service"/>
            <argument id="companies_house_module.clients.submission_queue_client" type="service"/>

            <tag name="cron.command" command-name="submission:queue:send_reminders" action="sendReminders" />
        </service>

        <service class="CompaniesHouseModule\Emailers\SubmissionQueueReminderEmailer" id="companies_house_module.emailers.submission_queue_reminder_emailer">
            <argument id="email_module.gateways.default_gateway" type="service"/>
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
            <argument id="id_module.repositories.id_info_repository" type="service"/>
            <argument type="service" id="user_module.creators.one_time_password_auth_token_creator"/>
            <argument id="router_module.helpers.controller_helper" type="service"/>
            <argument id="repositories.nodes.node_repository" type="service"/>
        </service>

        <service class="CompaniesHouseModule\Providers\SubmissionQueueReminderProvider" id="companies_house_module.providers.submission_queue_reminder_provider">
            <argument>%companies_house_module.submission_queue_reminders%</argument>
            <argument id="services.event_service" type="service"/>
            <argument id="companies_house_module.services.submission_handler" type="service"/>
        </service>

        <service class="CompaniesHouseModule\Services\SubmissionReviewService" id="companies_house_module.services.submission_review_service">
            <argument id="companies_house_module.repositories.review_submission_queue_repository" type="service"/>
            <argument id="services.event_service" type="service"/>
            <argument id="companies_house_module.emailers.submission_queue_emailer" type="service"/>
            <argument id="symfony.event_dispatcher" type="service"/>
        </service>

        <service class="CompaniesHouseModule\Listeners\ReviewSubmissionQueueListener" id="companies_house_module.listeners.review_submission_queue_listener">
            <argument id="companies_house_module.services.submission_review_service" type="service"/>
            <tag name="kernel.event_subscriber" />
        </service>

        <service class="CompaniesHouseModule\Commands\SubmitValidCompaniesFromQueueCommand" id="companies_house_module.commands.submit_valid_companies_from_queue_command">
            <argument id="companies_house_module.repositories.id_submission_queue_repository" type="service"/>
            <argument id="cron.loggers.default_logger" type="service" />
            <argument id="id_module.verification.validation_checker" type="service" />
            <argument id="companies_house_module.services.submission_handler" type="service" />

            <tag name="cron.command" command-name="submission:queue:submit_companies" action="submitCompanies" />
        </service>

        <service class="CompaniesHouseModule\Commands\ErrorScanResubmissionCommand" id="companies_house_module.commands.error_scan_resubmission_command">
            <argument id="cron.loggers.default_logger" type="service"/>
            <argument id="services.company_service" type="service"/>
            <argument type="service" id="services.submission_service"/>
            <argument type="service" id="companies_house_module.updaters.legacy.submission_updater"/>
            <argument type="service" id="companies_house_module.repositories.legacy.envelope_repository"/>
            <tag name="cron.command" command-name="resubmit:scan:submissions" action="resubmit" />
        </service>

        <service class="CompaniesHouseModule\Controllers\Admin\AnnualReturnController" id="companies_house_module.controllers.admin.annual_return_controller">
            <argument id="router_module.helpers.controller_helper" type="service"/>
            <argument type="service" id="error.loggers.monolog"/>
            <argument id="companies_house_module.facade.admin.annual_return_remove_facade" type="service"/>
        </service>

        <service class="CompaniesHouseModule\Facade\Admin\AnnualReturnRemoveFacade" id="companies_house_module.facade.admin.annual_return_remove_facade">
            <argument id="repositories.company_repository" type="service"/>
            <argument id="repositories.company_house.form_submission.annual_return_repository" type="service"/>
        </service>

        <service class="CompaniesHouseModule\Commands\ProcessPendingIdCheckOnSubmissionQueue" id="companies_house_module.commands.process_pending_id_check_on_submission_queue">
            <argument id="cron.loggers.default_logger" type="service"/>
            <argument id="companies_house_module.repositories.id_submission_queue_repository" type="service"/>
            <argument id="companies_house_module.services.submission_handler" type="service"/>

            <tag name="cron.command" command-name="submission:queue:process_ready_submissions" action="processReadyFormSubmissions" />
        </service>

        <service class="CompaniesHouseModule\Validator\Constraints\HighRiskCountryValidator" id="companies_house_module.validator.constraints.high_risk_country_validator">
            <tag name="validator.constraint_validator" alias="companies_house_module.forms.validators.constraints.high_risk_country_validator" />
        </service>

        <service class="CompaniesHouseModule\Validator\Constraints\HighRiskCountryOfResidenceValidator" id="companies_house_module.validator.constraints.high_risk_country_of_residence_validator">
            <tag name="validator.constraint_validator" alias="companies_house_module.forms.validators.constraints.high_risk_country_of_residence_validator" />
        </service>

        <service class="CompaniesHouseModule\Controllers\Admin\OfficersController" id="companies_house_module.controllers.admin.officers_controller">
            <argument id="templating_module.renderers.admin_renderer" type="service"/>
            <argument id="url_generator" type="service"/>
        </service>

        <service id="companies_house_module.commands.fix_incorrect_ownership_of_shares" class="CompaniesHouseModule\Commands\FixIncorrectOwnershipOfShares">
            <argument type="service" id="loggers.cron_logger_psr"/>
            <argument type="service" id="companies_house_module.loggers.ownership_of_shares_logger"/>
            <argument type="service" id="companies_house_module.services.member_service"/>

            <tag name="cron.command" command-name="services:fix_incorrect_ownership_of_shares" action="execute" />
        </service>

        <service class="CompaniesHouseModule\Loggers\OwnershipOfSharesLogger" id="companies_house_module.loggers.ownership_of_shares_logger">
            <argument id="monolog.logger.ownership_of_shares" type="service"/>
        </service>

        <service class="CompaniesHouseModule\Deciders\CompaniesHouseReadinessDecider" id="companies_house_module.deciders.companies_house_readiness_decider">
        </service>

        <service class="CompaniesHouseModule\Deciders\CompaniesHouseSchemaUpdateDecider" id="companies_house_module.deciders.companies_house_schema_update_decider">
            <argument id="feature_module.factories.feature_manager_factory" type="service"/>
        </service>

        <service class="CompaniesHouseModule\Deciders\CHExtraFeeChargeDecider" id="companies_house_module.deciders.ch_extra_fee_charge_decider">
            <argument id="services.company_service" type="service"/>
            <argument id="product_module.repositories.product_repository" type="service"/>
        </service>

        <service class="CompaniesHouseModule\Commands\CreditLimitResubmissionCommand" id="companies_house_module.commands.credit_limit_resubmission_command">
            <argument id="cron.loggers.default_logger" type="service"/>
            <argument id="services.company_service" type="service"/>
            <argument id="services.submission_service" type="service"/>
            <argument id="companies_house_module.repositories.legacy.envelope_repository" type="service"/>

            <tag name="cron.command" command-name="companies_house:fix:credit_limit" action="execute" />
        </service>

        <service class="CompaniesHouseModule\Clients\SubmissionQueueClient" id="companies_house_module.clients.submission_queue_client">
            <argument id="id_check_module.clients.http_client" type="service"/>
            <argument id="url_generator" type="service"/>
            <argument>%api.header-auth-name%</argument>
            <argument>%api.auth-token%</argument>
        </service>

        <service class="CompaniesHouseModule\Controllers\Api\SubmissionQueueApiController" id="companies_house_module.controllers.api.submission_queue_api_controller">
            <argument id="companies_house_module.providers.submission_queue_reminder_provider" type="service"/>
            <argument id="companies_house_module.emailers.submission_queue_reminder_emailer" type="service"/>
            <argument id="services.event_service" type="service"/>
        </service>

        <service class="CompaniesHouseModule\Consumers\SubmissionQueueConsumer" id="companies_house_module.consumers.submission_queue_consumer">
            <argument id="companies_house_module.repositories.id_submission_queue_repository" type="service"/>
            <argument id="companies_house_module.services.submission_handler" type="service"/>
            <tag name="pubsub.consumer" />
        </service>

        <service class="CompaniesHouseModule\Controllers\Admin\ChErrorController" id="companies_house_module.controllers.admin.ch_error_controller">
            <argument id="templating_module.renderers.admin_renderer" type="service"/>
            <argument id="companies_house_module.repositories.form_submission_repository" type="service"/>
            <argument id="url_generator" type="service"/>
        </service>


    </services>
</container>
