<?php

namespace CompaniesHouseModule\Services;

use CompaniesHouseModule\Deciders\CHExtraFeeChargeDecider;
use CompaniesHouseModule\Emailers\SubmissionQueueEmailer;
use CompaniesHouseModule\Entities\FormSubmission\OfficerAppointment;
use CompaniesHouseModule\Entities\IdSubmissionQueue;
use CompaniesHouseModule\Repositories\IdSubmissionQueueRepository;
use CompaniesHouseModule\Repositories\ReviewSubmissionQueueRepository;
use CompanyFormationModule\Repositories\FormSubmissionRepository;
use IdModule\Domain\IdEntity;
use IdModule\Repositories\EntityProvider;
use IdModule\Repositories\IdInfoRepository;
use Libs\CHFiling\Core\Company;
use CompanyModule\ICompany;
use Services\Dispatcher\Events\CompanyEvent;
use Entities\CompanyHouse\FormSubmission as FormSubmissionEntity;
use Exception;
use Exceptions\Technical\RequestException;
use Libs\CHFiling\Core\Request\FormSubmission;
use Legacy\Nette\Exceptions\InvalidStateException;
use OrmModule\Repositories\IPersistRepository;
use IdModule\Repositories\IIdCompanyInfoRepository;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class SubmissionHandler
{
    public function __construct(
        private readonly IPersistRepository $repository,
        private readonly IdSubmissionQueueRepository $idSubmissionQueueRepository,
        private readonly SubmissionQueueEmailer $emailer,
        private readonly EventDispatcherInterface $eventDispatcher,
        private readonly IIdCompanyInfoRepository $iIdCompanyInfoRepository,
        private readonly ChExtraFeeChargeDecider $chExtraFeeChargeDecider,
        private readonly EntityProvider $entityProvider,
        private readonly IdInfoRepository $idEntityInfoRepository,
        private readonly FormSubmissionRepository $formSubmissionRepository,
        private readonly ReviewSubmissionQueueRepository $reviewSubmissionQueueRepository
    ) {
    }

    public function canSubmitCompany(?ICompany $company): bool
    {
        if (is_null($company)) return false;
        if (!$this->isCompanyIdValid($company)) return false;
        if ($this->isExtraFeeChargeApplicable($company)) return false;

        return true;
    }

    // TODO: Remove and move back to the canSubmitCompany when extra fee charge can be removed
    public function isCompanyIdValid(ICompany $company): bool
    {
        if ($companyInfo = $this->iIdCompanyInfoRepository->getIdInfoForCompany($company))
            return $companyInfo->isValid();

        return false;
    }

    // TODO: Remove and move back to the canSubmitCompany when extra fee charge can be removed
    public function isExtraFeeChargeApplicable(ICompany $company, bool $ignoreConfirmationStatement = false): bool
    {
        if (!$company->isIncorporated() && $this->chExtraFeeChargeDecider->isIncorporationExtraFeeChargeApplicable($company))
            return true;

        if (!$ignoreConfirmationStatement && $this->chExtraFeeChargeDecider->isConfirmationStatementExtraFeeChargeApplicable($company))
            return true;

        return false;
    }

    public function withHoldSubmission(ICompany $company, $formSubmissionId): IdSubmissionQueue
    {
        $this->lockSubmission($company, $formSubmissionId);

        /** @var IdSubmissionQueue $submissionQueue */
        $submissionQueue = $this->idSubmissionQueueRepository->optionalOneBy(
            [
                'company' => $company,
                'formSubmissionId' => $formSubmissionId
            ]
        );

        if ($submissionQueue) return $submissionQueue;

        $queue = $this->addToIdQueue($company, $formSubmissionId);
        $this->emailer->sendWithHoldSubmission($company);
        return $queue;

    }

    public function addToIdQueue(ICompany $company, $formSubmissionId): IdSubmissionQueue
    {
        $queue = new IdSubmissionQueue($company, $formSubmissionId);
        return $this->repository->saveEntity($queue);
    }

    /**
     * @param IdSubmissionQueue $queue
     * @throws Exception
     * @throws RequestException|InvalidStateException
     */
    public function submit(IdSubmissionQueue $queue)
    {
        $formSubmission = $this->submitSubmission($queue);
        $this->repository->removeEntity($queue);
        $this->eventDispatcher->dispatch(
            new CompanyEvent($queue->getCompany(), $formSubmission),
            'events.submission_queue.submit'
        );
    }

    public function reject(IdSubmissionQueue $queue)
    {
        $company = $queue->getCompany();
        $company->setLocked(true);
        $this->unlockSubmission($company, $queue->getFormSubmissionId());
        $this->repository->saveEntity($company);
        $this->repository->removeEntity($queue);
    }

    public function removeSubmissionFromQueue(ICompany $company, $formSubmissionId): void
    {
        $submissionQueue = $this->idSubmissionQueueRepository->optionalOneBy(
            [
                'company' => $company,
                'formSubmissionId' => $formSubmissionId
            ]
        );

        if ($submissionQueue) {
            $this->repository->removeEntity($submissionQueue);
        }
    }

    public function removeReviewSubmissionFromQueue(ICompany $company, $formSubmissionId): void
    {
        $submissionQueue = $this->reviewSubmissionQueueRepository->optionalOneBy(
            [
                'company' => $company,
                'formSubmissionId' => $formSubmissionId
            ]
        );

        if ($submissionQueue) {
            $this->repository->removeEntity($submissionQueue);
        }
    }

    public function removeSubmissionsFromQueueForCompany(ICompany $company): void
    {
        $submissionQueueItems = $this->idSubmissionQueueRepository->findBy(
            [
                'company' => $company
            ]
        );

        if ($submissionQueueItems) {
            foreach ($submissionQueueItems as $submissionQueueItem) {
                $this->repository->removeEntity($submissionQueueItem);
            }
        }
    }

    public function canSubmit(IdSubmissionQueue $submissionQueueItem): bool
    {
        /** @var FormSubmissionEntity $formSubmission */
        $formSubmission = $this->formSubmissionRepository->find($submissionQueueItem->getFormSubmissionId());

        /** @phpstan-ignore-next-line */
        if ($submissionQueueItem->getCompany()->isIncorporated() && $formSubmission instanceof OfficerAppointment) {
            return $this->canSubmitOfficer($submissionQueueItem, $formSubmission);
        }

        return $submissionQueueItem->getCompany()->isIncorporated() || $this->canSubmitCompany($submissionQueueItem->getCompany());
    }

    private function canSubmitOfficer(IdSubmissionQueue $submissionQueueItem, OfficerAppointment $formSubmission): bool
    {
        if (!$formSubmission->isDirectorAppointment()) {
            return true;
        }

        /** @var IdEntity $entity */
        foreach($this->entityProvider->getOfficerAppointmentsEntities($submissionQueueItem->getCompany()) as $entity) {
            if (!in_array($submissionQueueItem->getFormSubmissionId(), $entity->getMemberIds())) {
                continue;
            }

            return $this->idEntityInfoRepository->getIdInfoForEntity($entity)->isValid();
        }

        return false;
    }

    /**
     * @param ICompany $company
     * @param string $formSubmissionId
     * @return FormSubmission
     * @throws Exception
     */
    private function getSubmission(ICompany $company, $formSubmissionId)
    {
        return FormSubmission::getFormSubmission(Company::getCompany($company->getId()), $formSubmissionId);
    }

    private function lockSubmission(ICompany $company, $formSubmissionId)
    {
        $submission = $this->getSubmission($company, $formSubmissionId);
        $submission->setResponse(FormSubmissionEntity::RESPONSE_WITHHOLD);
    }

    private function unlockSubmission(ICompany $company, $formSubmissionId)
    {
        $submission = $this->getSubmission($company, $formSubmissionId);
        $submission->nullifyResponse();
    }

    private function submitSubmission(IdSubmissionQueue $queue): FormSubmission
    {
        $submission = $this->getSubmission($queue->getCompany(), $queue->getFormSubmissionId());
        $submission->sendRequestImproved();
        return $submission;
    }

    public function changeSubmissionToDeleted(ICompany $company, $formSubmissionId): FormSubmission
    {
        $submission = $this->getSubmission($company, $formSubmissionId);
        $submission->changeToDeleted();
        return $submission;
    }

    public function changePendingSubmissionsToDeleted(ICompany $company): int
    {
        $count = 0;
        foreach (FormSubmission::getPendingCompanyFormSubmissionsIds($company->getId()) as $formSubmissionsId) {
            $this->changeSubmissionToDeleted($company, $formSubmissionsId);
            $count++;
        }
        return $count;
    }
}
