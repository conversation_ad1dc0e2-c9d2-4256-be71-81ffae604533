<?php

namespace AdminModule\Factories;

use AdminModule\Views\CompaniesAdminControlerView;
use CompaniesHouseModule\Services\SubmissionHandler;
use CompanyFormationModule\Repositories\SicCodesRepository;
use Entities\Company;
use FormSubmissionModule\Factories\FormSubmissionsViewFactory;

readonly class CompaniesAdminControlerViewFactory
{
    
    public function __construct(
        private FormSubmissionsViewFactory $formSubmissionsViewFactory,
        private SicCodesRepository $sicCodesRepository,
        private SubmissionHandler $submissionHandler
    ) {}
    
    public function create(Company $company): CompaniesAdminControlerView
    {
        return new CompaniesAdminControlerView(
            $this->formSubmissionsViewFactory->createFromCompany($company),
            $this->sicCodesRepository->getCompanySicCodesWithDesctiption($company),
            $this->submissionHandler
        );
    }
}
