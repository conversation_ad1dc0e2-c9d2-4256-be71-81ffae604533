<?php

namespace AdminModule\Views;

use CompaniesHouseModule\Entities\IdSubmissionQueue;
use CompaniesHouseModule\Services\SubmissionHandler;
use Entities\Company as CompanyEntity;
use FormSubmissionModule\Views\FormSubmissionsView;
use FormSubmissionModule\Views\FormSubmissionView;
use Framework\FUser;

readonly class CompaniesAdminControlerView
{
    public function __construct(
        private FormSubmissionsView $formSubmissionsView,
        private array $sicCodes,
        private SubmissionHandler $submissionHandler
    ) {}

    public function hasFormSubmissions(): bool
    {
        return (bool) $this->formSubmissionsView->getFormSubmissionViews();
    }

    public function getFormSubmissions(): array
    {
        return $this->formSubmissionsView->getFormSubmissionViews();
    }

    public function canResubmit(FUser $user, FormSubmissionView $formSubmissionView): bool
    {
        return $user->role->isAdmin() && $formSubmissionView->isResubmittable();
    }

    public function canRepoll(FUser $user, FormSubmissionView $formSubmissionView): bool
    {
        return $formSubmissionView->isRepollable();
    }

    public function canRemoveError(FUser $user, FormSubmissionView $formSubmissionView): bool
    {
        return $formSubmissionView->isError();
    }

    public function canShowInternalFailureWarning(): bool
    {
        return $this->formSubmissionsView->hasInternalFailureSubmission();
    }

    public function canViewMinutes(FormSubmissionView $formSubmissionView): bool
    {
        return $formSubmissionView->isViewable();
    }

    public function canRemoveLink(): bool
    {
        foreach ($this->getFormSubmissions() as $formSubmissionView) {
            if (!$formSubmissionView->isCancelled() && $formSubmissionView->isPending() && $formSubmissionView->isCompanyIncorporationType()) {
                return true;
            }
        }

        return false;
    }

    public function getSicCodes(): array
    {
        return $this->sicCodes;
    }

    public function canSubmitForm(CompanyEntity $company, int $formSubmissionId): bool
    {
        $submissionQueue = new IdSubmissionQueue(
            $company,
            $formSubmissionId
        );

        return $this->submissionHandler->canSubmit($submissionQueue);
    }
}
